import 'package:flutter_test/flutter_test.dart';
import 'package:wakeup_app/models/knowledge_node_model.dart';
import 'package:wakeup_app/services/mindmap_service.dart';
import 'package:wakeup_app/providers/mindmap_provider.dart';

/// 思维导图功能测试
/// 
/// 测试知识节点模型、服务和状态管理的基本功能
void main() {
  group('KnowledgeNode 测试', () {
    test('应该能创建根节点', () {
      final rootNode = KnowledgeNode.createRoot('机器学习');
      
      expect(rootNode.title, equals('机器学习'));
      expect(rootNode.isRoot, isTrue);
      expect(rootNode.isExpanded, isTrue);
      expect(rootNode.level, equals(0));
      expect(rootNode.parentId, isNull);
    });

    test('应该能从API响应创建节点', () {
      final apiData = {
        'title': '什么是机器学习？',
        'content': '机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习。',
        'children': [
          '监督学习是什么？',
          '无监督学习是什么？',
          '强化学习是什么？',
        ],
      };

      final node = KnowledgeNode.fromApiResponse(apiData);
      
      expect(node.title, equals('什么是机器学习？'));
      expect(node.content, contains('机器学习是人工智能'));
      expect(node.children.length, equals(3));
      expect(node.children[0].title, equals('监督学习是什么？'));
    });

    test('应该能复制并更新节点', () {
      final originalNode = KnowledgeNode.createRoot('原始主题');
      final updatedNode = originalNode.copyWith(
        title: '更新的主题',
        isExpanded: false,
      );
      
      expect(updatedNode.title, equals('更新的主题'));
      expect(updatedNode.isExpanded, isFalse);
      expect(updatedNode.id, equals(originalNode.id)); // ID应该保持不变
    });

    test('应该能获取展开的节点列表', () {
      final rootNode = KnowledgeNode(
        id: 'root',
        title: '根节点',
        content: '根节点内容',
        isExpanded: true,
        children: [
          KnowledgeNode(
            id: 'child1',
            title: '子节点1',
            content: '子节点1内容',
            isExpanded: true,
            level: 1,
            parentId: 'root',
            children: [
              KnowledgeNode(
                id: 'grandchild1',
                title: '孙节点1',
                content: '孙节点1内容',
                level: 2,
                parentId: 'child1',
              ),
            ],
          ),
          KnowledgeNode(
            id: 'child2',
            title: '子节点2',
            content: '子节点2内容',
            level: 1,
            parentId: 'root',
          ),
        ],
      );

      final expandedNodes = rootNode.getExpandedNodes();
      
      expect(expandedNodes.length, equals(4)); // 根节点 + 2个子节点 + 1个孙节点
      expect(expandedNodes[0].id, equals('root'));
      expect(expandedNodes[1].id, equals('child1'));
      expect(expandedNodes[2].id, equals('grandchild1'));
      expect(expandedNodes[3].id, equals('child2'));
    });
  });

  group('MindMapService 测试', () {
    late MindMapService service;

    setUp(() {
      service = MindMapService();
    });

    tearDown(() {
      service.dispose();
    });

    test('应该能创建服务实例', () {
      expect(service, isNotNull);
    });

    // 注意：实际的网络请求测试需要模拟网络环境
    // 这里只测试基本的服务创建和销毁
  });

  group('MindMapProvider 测试', () {
    late MindMapProvider provider;

    setUp(() {
      provider = MindMapProvider();
    });

    tearDown(() {
      provider.dispose();
    });

    test('应该有正确的初始状态', () {
      expect(provider.state, equals(MindMapState.initial));
      expect(provider.rootNode, isNull);
      expect(provider.isLoading, isFalse);
      expect(provider.hasError, isFalse);
    });

    test('应该能获取统计信息', () {
      final stats = provider.getStatistics();
      
      expect(stats['totalNodes'], equals(0));
      expect(stats['expandedNodes'], equals(0));
      expect(stats['maxLevel'], equals(0));
    });

    test('应该能检查节点展开状态', () {
      expect(provider.isNodeExpanding('test-node'), isFalse);
    });
  });

  group('MindMapState 和 MindMapError 测试', () {
    test('应该有正确的状态枚举', () {
      expect(MindMapState.values.length, equals(5));
      expect(MindMapState.values, contains(MindMapState.initial));
      expect(MindMapState.values, contains(MindMapState.loading));
      expect(MindMapState.values, contains(MindMapState.loaded));
      expect(MindMapState.values, contains(MindMapState.expanding));
      expect(MindMapState.values, contains(MindMapState.error));
    });

    test('应该能创建错误对象', () {
      const error = MindMapError(
        message: '测试错误',
        code: 'TEST_ERROR',
        details: '错误详情',
      );
      
      expect(error.message, equals('测试错误'));
      expect(error.code, equals('TEST_ERROR'));
      expect(error.details, equals('错误详情'));
    });
  });
}
