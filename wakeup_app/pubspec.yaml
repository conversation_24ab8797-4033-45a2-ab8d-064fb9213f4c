name: wakeup_app
description: "题魔方 - 智能学习答题应用"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.7.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  cupertino_icons: ^1.0.8
  http: ^1.3.0
  provider: ^6.0.5
  shared_preferences: ^2.2.2
  shimmer: ^3.0.0
  extended_image: ^8.1.0
  path_provider: ^2.1.2
  font_awesome_flutter: ^10.8.0
  image_picker: ^1.0.5
  
  # OAuth 2.0 社交登录依赖包
  sign_in_with_apple: ^6.1.2
  # fluwx: ^4.0.3  # 临时禁用，等SDK问题解决
  # tencent_kit: ^6.2.0  # 临时禁用，等SDK问题解决
  google_sign_in: ^6.2.1
  sensors_plus: ^6.1.2
  liquid_glass_renderer: ^0.1.1-dev.6
  mesh: ^0.5.0
  graphview: ^1.2.0

# OAuth 2.0 社交登录平台配置
# tencent_kit:  # 临时禁用
#   app_id: **********  # 临时占位符，需要替换为真实的QQ APP ID
#   universal_link: https://wakeup.example.com/qq_conn/**********/

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

# 应用图标配置 - 支持深色浅色模式自适应
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icons/cube_icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/cube_icon.png"
  remove_alpha_ios: false
  min_sdk_android: 21
  ios_content_mode: scaleAspectFit
  image_path_ios: "assets/icons/cube_icon.png"
  web:
    generate: false
  windows:
    generate: false
  macos:
    generate: false
  create_icons_for_all_sizes: true

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/images/icons/
    - assets/images/learning/
    - assets/icons/
    - assets/translations/
    - assets/images/learning_bg1.png
    - assets/images/learning_bg2.png
    - assets/images/learning_bg3.png
    - assets/images/traditional_quiz_bg.png


  fonts:
    - family: Noto Sans SC
      fonts:
        - asset: assets/fonts/Noto_Sans_SC/static/NotoSansSC-Regular.ttf
        - asset: assets/fonts/Noto_Sans_SC/static/NotoSansSC-Medium.ttf
          weight: 500
        - asset: assets/fonts/Noto_Sans_SC/static/NotoSansSC-Bold.ttf
          weight: 700
    
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto/static/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto/static/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto/static/Roboto-Bold.ttf
          weight: 700