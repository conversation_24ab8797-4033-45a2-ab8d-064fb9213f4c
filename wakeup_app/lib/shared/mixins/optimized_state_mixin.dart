import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

/// 优化状态管理的 Mixin，减少不必要的 setState 调用
mixin OptimizedStateMixin<T extends StatefulWidget> on State<T> {
  bool _isDisposed = false;
  final Set<String> _pendingUpdates = {};
  Timer? _debounceTimer;

  @override
  void dispose() {
    _isDisposed = true;
    _debounceTimer?.cancel();
    super.dispose();
  }

  /// 安全的 setState，自动检查 mounted 状态
  void safeSetState(VoidCallback fn, [String? updateKey]) {
    if (_isDisposed || !mounted) return;

    if (updateKey != null) {
      // 防重复更新
      if (_pendingUpdates.contains(updateKey)) return;
      _pendingUpdates.add(updateKey);
    }

    setState(() {
      fn();
      if (updateKey != null) {
        _pendingUpdates.remove(updateKey);
      }
    });
  }

  /// 防抖 setState，在短时间内多次调用时只执行最后一次
  void debouncedSetState(
    VoidCallback fn, {
    Duration delay = const Duration(milliseconds: 100),
    String? updateKey,
  }) {
    if (_isDisposed || !mounted) return;

    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, () {
      safeSetState(fn, updateKey);
    });
  }

  /// 批量状态更新，减少多次 setState
  void batchSetState(List<VoidCallback> updates) {
    if (_isDisposed || !mounted || updates.isEmpty) return;

    setState(() {
      for (final update in updates) {
        update();
      }
    });
  }

  /// 条件性 setState，只在值真正改变时才更新
  void conditionalSetState<V>(
    V newValue,
    V currentValue,
    ValueSetter<V> setter,
  ) {
    if (_isDisposed || !mounted || newValue == currentValue) return;

    setState(() {
      setter(newValue);
    });
  }
}

/// 性能监控 Mixin
mixin PerformanceMonitorMixin<T extends StatefulWidget> on State<T> {
  late final Stopwatch _buildStopwatch = Stopwatch();
  int _buildCount = 0;

  @override
  Widget build(BuildContext context) {
    _buildStopwatch.start();
    final widget = performanceBuild(context);
    _buildStopwatch.stop();

    _buildCount++;

    // 开发模式下记录性能数据
    if (kDebugMode && _buildStopwatch.elapsedMilliseconds > 16) {
      debugPrint(
        '⚠️ [Performance] ${T.toString()} build took ${_buildStopwatch.elapsedMilliseconds}ms '
        '(count: $_buildCount)',
      );
    }

    _buildStopwatch.reset();
    return widget;
  }

  /// 子类实现具体的 build 逻辑
  Widget performanceBuild(BuildContext context);
}
