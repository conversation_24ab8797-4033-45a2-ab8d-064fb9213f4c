import 'package:flutter/widgets.dart';

/// 加载状态管理Mixin
/// 统一处理加载状态，避免在多个页面重复实现
mixin LoadingMixin on ChangeNotifier {
  bool _isLoading = false;
  String? _loadingMessage;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 加载消息
  String? get loadingMessage => _loadingMessage;

  /// 设置加载状态
  void setLoading(bool loading, [String? message]) {
    if (_isLoading != loading || _loadingMessage != message) {
      _isLoading = loading;
      _loadingMessage = message;
      notifyListeners();
    }
  }

  /// 开始加载
  void startLoading([String? message]) {
    setLoading(true, message);
  }

  /// 停止加载
  void stopLoading() {
    setLoading(false);
  }

  /// 执行异步操作并管理加载状态
  Future<T?> withLoading<T>(
    Future<T> Function() operation, {
    String? loadingMessage,
    bool showError = true,
  }) async {
    try {
      startLoading(loadingMessage);
      final result = await operation();
      return result;
    } catch (e) {
      if (showError) {
        handleError(e);
      }
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// 处理错误 - 子类可重写
  void handleError(Object error) {
    // 默认实现：打印错误
    debugPrint('加载错误: $error');
  }
}

/// 页面状态管理Mixin
/// 处理页面的初始化、刷新、错误等状态
mixin PageStateMixin<T extends StatefulWidget> on State<T> {
  bool _isInitialized = false;
  bool _isRefreshing = false;
  String? _errorMessage;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 是否正在刷新
  bool get isRefreshing => _isRefreshing;

  /// 错误消息
  String? get errorMessage => _errorMessage;

  /// 是否有错误
  bool get hasError => _errorMessage != null;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      initializePage();
    });
  }

  /// 初始化页面
  Future<void> initializePage() async {
    if (_isInitialized) return;

    try {
      await onInitialize();
      setState(() {
        _isInitialized = true;
        _errorMessage = null;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    }
  }

  /// 刷新页面
  Future<void> refreshPage() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
      _errorMessage = null;
    });

    try {
      await onRefresh();
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  /// 清除错误
  void clearError() {
    setState(() {
      _errorMessage = null;
    });
  }

  /// 子类需要实现的初始化方法
  Future<void> onInitialize();

  /// 子类需要实现的刷新方法
  Future<void> onRefresh() async {
    await onInitialize();
  }
}

/// 分页加载Mixin
/// 处理列表的分页加载逻辑
mixin PaginationMixin<T> on ChangeNotifier {
  final List<T> _items = [];
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  final int _pageSize = 20;

  /// 数据列表
  List<T> get items => List.unmodifiable(_items);

  /// 是否正在加载更多
  bool get isLoadingMore => _isLoadingMore;

  /// 是否还有更多数据
  bool get hasMoreData => _hasMoreData;

  /// 当前页码
  int get currentPage => _currentPage;

  /// 页面大小
  int get pageSize => _pageSize;

  /// 重置分页状态
  void resetPagination() {
    _items.clear();
    _currentPage = 1;
    _hasMoreData = true;
    _isLoadingMore = false;
    notifyListeners();
  }

  /// 加载首页数据
  Future<void> loadFirstPage() async {
    resetPagination();
    await loadMoreData();
  }

  /// 加载更多数据
  Future<void> loadMoreData() async {
    if (_isLoadingMore || !_hasMoreData) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final newItems = await fetchPageData(_currentPage, _pageSize);
      
      if (newItems.isEmpty || newItems.length < _pageSize) {
        _hasMoreData = false;
      }

      _items.addAll(newItems);
      _currentPage++;
    } catch (e) {
      handlePaginationError(e);
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// 添加单个项目
  void addItem(T item) {
    _items.add(item);
    notifyListeners();
  }

  /// 更新项目
  void updateItem(int index, T item) {
    if (index >= 0 && index < _items.length) {
      _items[index] = item;
      notifyListeners();
    }
  }

  /// 删除项目
  void removeItem(int index) {
    if (index >= 0 && index < _items.length) {
      _items.removeAt(index);
      notifyListeners();
    }
  }

  /// 子类需要实现的数据获取方法
  Future<List<T>> fetchPageData(int page, int pageSize);

  /// 处理分页错误 - 子类可重写
  void handlePaginationError(Object error) {
    debugPrint('分页加载错误: $error');
  }
}

/// 搜索功能Mixin
/// 处理搜索相关的状态和逻辑
mixin SearchMixin<T> on ChangeNotifier {
  String _searchQuery = '';
  List<T> _searchResults = [];
  bool _isSearching = false;

  /// 搜索关键词
  String get searchQuery => _searchQuery;

  /// 搜索结果
  List<T> get searchResults => List.unmodifiable(_searchResults);

  /// 是否正在搜索
  bool get isSearching => _isSearching;

  /// 是否有搜索结果
  bool get hasSearchResults => _searchResults.isNotEmpty;

  /// 执行搜索
  Future<void> search(String query) async {
    if (_searchQuery == query) return;

    _searchQuery = query;
    _isSearching = true;
    notifyListeners();

    try {
      if (query.isEmpty) {
        _searchResults.clear();
      } else {
        _searchResults = await performSearch(query);
      }
    } catch (e) {
      handleSearchError(e);
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// 清除搜索
  void clearSearch() {
    _searchQuery = '';
    _searchResults.clear();
    notifyListeners();
  }

  /// 子类需要实现的搜索方法
  Future<List<T>> performSearch(String query);

  /// 处理搜索错误 - 子类可重写
  void handleSearchError(Object error) {
    debugPrint('搜索错误: $error');
  }
}