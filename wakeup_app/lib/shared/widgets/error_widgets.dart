import 'package:flutter/cupertino.dart';
import '../../constants/fonts.dart';

/// 统一的错误显示组件库
/// 解决项目中重复的错误处理UI代码

/// 基础错误显示组件
class AppErrorWidget extends StatelessWidget {
  final String message;
  final String? title;
  final IconData? icon;
  final VoidCallback? onRetry;
  final String? retryText;

  const AppErrorWidget({
    super.key,
    required this.message,
    this.title,
    this.icon,
    this.onRetry,
    this.retryText,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 错误图标
            Icon(
              icon ?? CupertinoIcons.exclamationmark_circle,
              size: 64,
              color: CupertinoColors.systemRed,
            ),
            const SizedBox(height: 16),
            
            // 错误标题
            if (title != null) ...[
              Text(
                title!,
                style: AppFonts.createMixedStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.label,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
            
            // 错误消息
            Text(
              message,
              style: AppFonts.createMixedStyle(
                fontSize: 14,
                color: CupertinoColors.systemGrey,
              ),
              textAlign: TextAlign.center,
            ),
            
            // 重试按钮
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              CupertinoButton.filled(
                onPressed: onRetry,
                child: Text(retryText ?? '重试'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 网络错误组件
class AppNetworkError extends StatelessWidget {
  final VoidCallback? onRetry;

  const AppNetworkError({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return AppErrorWidget(
      title: '网络连接失败',
      message: '请检查网络连接后重试',
      icon: CupertinoIcons.wifi_slash,
      onRetry: onRetry,
    );
  }
}

/// 数据为空组件
class AppEmptyWidget extends StatelessWidget {
  final String message;
  final String? title;
  final IconData? icon;
  final VoidCallback? onRefresh;
  final String? refreshText;

  const AppEmptyWidget({
    super.key,
    this.message = '暂无数据',
    this.title,
    this.icon,
    this.onRefresh,
    this.refreshText,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 空状态图标
            Icon(
              icon ?? CupertinoIcons.doc_text,
              size: 64,
              color: CupertinoColors.systemGrey,
            ),
            const SizedBox(height: 16),
            
            // 标题
            if (title != null) ...[
              Text(
                title!,
                style: AppFonts.createMixedStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.label,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
            
            // 空状态消息
            Text(
              message,
              style: AppFonts.createMixedStyle(
                fontSize: 14,
                color: CupertinoColors.systemGrey,
              ),
              textAlign: TextAlign.center,
            ),
            
            // 刷新按钮
            if (onRefresh != null) ...[
              const SizedBox(height: 24),
              CupertinoButton(
                onPressed: onRefresh,
                child: Text(
                  refreshText ?? '刷新',
                  style: AppFonts.createMixedStyle(
                    fontSize: 16,
                    color: CupertinoColors.systemBlue,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 权限错误组件
class AppPermissionError extends StatelessWidget {
  final String? message;
  final VoidCallback? onGrantPermission;

  const AppPermissionError({
    super.key,
    this.message,
    this.onGrantPermission,
  });

  @override
  Widget build(BuildContext context) {
    return AppErrorWidget(
      title: '权限不足',
      message: message ?? '需要相应权限才能访问此功能',
      icon: CupertinoIcons.lock,
      onRetry: onGrantPermission,
      retryText: '授权',
    );
  }
}

/// 服务器错误组件
class AppServerError extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const AppServerError({
    super.key,
    this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return AppErrorWidget(
      title: '服务器错误',
      message: message ?? '服务器暂时无法响应，请稍后重试',
      icon: CupertinoIcons.cloud_bolt,
      onRetry: onRetry,
    );
  }
}

/// 错误边界组件
class AppErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace? stackTrace)? errorBuilder;

  const AppErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
  });

  @override
  State<AppErrorBoundary> createState() => _AppErrorBoundaryState();
}

class _AppErrorBoundaryState extends State<AppErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _stackTrace);
      }
      return AppErrorWidget(
        title: '应用错误',
        message: '应用遇到了一个错误，请重启应用',
        onRetry: () => setState(() {
          _error = null;
          _stackTrace = null;
        }),
      );
    }

    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    FlutterError.onError = (FlutterErrorDetails details) {
      setState(() {
        _error = details.exception;
        _stackTrace = details.stack;
      });
    };
  }
}

/// 错误Toast提示
class AppErrorToast {
  static void show(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => _ErrorToastWidget(
        message: message,
        duration: duration,
      ),
    );

    overlay.insert(overlayEntry);

    // 自动移除
    Future.delayed(duration, () {
      overlayEntry.remove();
    });
  }
}

class _ErrorToastWidget extends StatefulWidget {
  final String message;
  final Duration duration;

  const _ErrorToastWidget({
    required this.message,
    required this.duration,
  });

  @override
  State<_ErrorToastWidget> createState() => _ErrorToastWidgetState();
}

class _ErrorToastWidgetState extends State<_ErrorToastWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeOut);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      left: 16,
      right: 16,
      child: FadeTransition(
        opacity: _animation,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, -1),
            end: Offset.zero,
          ).animate(_animation),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: CupertinoColors.systemRed,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: CupertinoColors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(
                  CupertinoIcons.exclamationmark_circle_fill,
                  color: CupertinoColors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.message,
                    style: AppFonts.createMixedStyle(
                      fontSize: 14,
                      color: CupertinoColors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}