/// 知识节点数据模型
///
/// 用于表示思维导图中的知识节点，包含节点的基本信息、状态和层级关系
class KnowledgeNode {
  /// 节点唯一标识符
  final String id;

  /// 节点标题（问句形式）
  final String title;

  /// 节点内容（回答标题问题的知识内容）
  final String content;

  /// 是否已展开
  final bool isExpanded;

  /// 节点层级（从0开始，根节点为0）
  final int level;

  /// 父节点ID（根节点为null）
  final String? parentId;

  /// 子节点列表
  final List<KnowledgeNode> children;

  /// 是否为根节点
  bool get isRoot => parentId == null;

  /// 是否有子节点
  bool get hasChildren => children.isNotEmpty;

  const KnowledgeNode({
    required this.id,
    required this.title,
    required this.content,
    this.isExpanded = false,
    this.level = 0,
    this.parentId,
    this.children = const [],
  });

  /// 从API响应创建知识节点
  ///
  /// 参数：
  /// - data: 后端API返回的数据
  /// - level: 节点层级
  /// - parentId: 父节点ID
  factory KnowledgeNode.fromApiResponse(
    Map<String, dynamic> data, {
    int level = 0,
    String? parentId,
  }) {
    final String nodeId = _generateNodeId();
    final List<String> childrenTitles = List<String>.from(
      data['children'] ?? [],
    );

    // 创建子节点（初始状态为未展开）
    final List<KnowledgeNode> children =
        childrenTitles.map((childTitle) {
          return KnowledgeNode(
            id: _generateNodeId(),
            title: childTitle,
            content: '', // 子节点内容在展开时获取
            isExpanded: false,
            level: level + 1,
            parentId: nodeId,
          );
        }).toList();

    return KnowledgeNode(
      id: nodeId,
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      isExpanded: level == 0, // 根节点默认展开
      level: level,
      parentId: parentId,
      children: children,
    );
  }

  /// 创建根节点
  ///
  /// 参数：
  /// - topic: 根节点主题
  factory KnowledgeNode.createRoot(String topic) {
    return KnowledgeNode(
      id: _generateNodeId(),
      title: topic,
      content: '正在加载知识内容...',
      isExpanded: true,
      level: 0,
      parentId: null,
      children: [],
    );
  }

  /// 复制节点并更新指定属性
  ///
  /// 参数：
  /// - id: 新ID
  /// - title: 新标题
  /// - content: 新内容
  /// - isExpanded: 新展开状态
  /// - level: 新层级
  /// - parentId: 新父节点ID
  /// - children: 新子节点列表
  KnowledgeNode copyWith({
    String? id,
    String? title,
    String? content,
    bool? isExpanded,
    int? level,
    String? parentId,
    List<KnowledgeNode>? children,
  }) {
    return KnowledgeNode(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      isExpanded: isExpanded ?? this.isExpanded,
      level: level ?? this.level,
      parentId: parentId ?? this.parentId,
      children: children ?? this.children,
    );
  }

  /// 展开节点（设置为已展开状态）
  KnowledgeNode expand() {
    return copyWith(isExpanded: true);
  }

  /// 更新子节点
  ///
  /// 参数：
  /// - newChildren: 新的子节点列表
  KnowledgeNode updateChildren(List<KnowledgeNode> newChildren) {
    return copyWith(children: newChildren);
  }

  /// 在子节点列表中查找指定ID的节点
  ///
  /// 参数：
  /// - nodeId: 要查找的节点ID
  ///
  /// 返回：找到的节点，如果未找到则返回null
  KnowledgeNode? findChildById(String nodeId) {
    for (final child in children) {
      if (child.id == nodeId) {
        return child;
      }
      final found = child.findChildById(nodeId);
      if (found != null) {
        return found;
      }
    }
    return null;
  }

  /// 递归更新指定ID的节点
  ///
  /// 参数：
  /// - nodeId: 要更新的节点ID
  /// - updatedNode: 更新后的节点
  ///
  /// 返回：更新后的当前节点
  KnowledgeNode updateNodeById(String nodeId, KnowledgeNode updatedNode) {
    if (id == nodeId) {
      return updatedNode;
    }

    final List<KnowledgeNode> updatedChildren =
        children.map((child) {
          return child.updateNodeById(nodeId, updatedNode);
        }).toList();

    return copyWith(children: updatedChildren);
  }

  /// 获取所有展开的节点（用于渲染）
  ///
  /// 返回：展开的节点列表，按层级顺序排列
  List<KnowledgeNode> getExpandedNodes() {
    final List<KnowledgeNode> result = [this];

    if (isExpanded) {
      for (final child in children) {
        result.addAll(child.getExpandedNodes());
      }
    }

    return result;
  }

  /// 转换为JSON格式（用于调试和序列化）
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'isExpanded': isExpanded,
      'level': level,
      'parentId': parentId,
      'children': children.map((child) => child.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'KnowledgeNode(id: $id, title: $title, level: $level, isExpanded: $isExpanded, children: ${children.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is KnowledgeNode && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// 生成唯一的节点ID
  ///
  /// 返回：基于时间戳和随机数的唯一ID
  static String _generateNodeId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'node_${timestamp}_$random';
  }
}

/// 思维导图状态枚举
///
/// 用于表示思维导图的不同状态
enum MindMapState {
  /// 初始状态
  initial,

  /// 加载中
  loading,

  /// 加载完成
  loaded,

  /// 展开节点中
  expanding,

  /// 错误状态
  error,
}

/// 思维导图错误信息
///
/// 用于封装思维导图操作中的错误信息
class MindMapError {
  /// 错误消息
  final String message;

  /// 错误代码
  final String? code;

  /// 错误详情
  final dynamic details;

  const MindMapError({required this.message, this.code, this.details});

  @override
  String toString() {
    return 'MindMapError(message: $message, code: $code)';
  }
}
