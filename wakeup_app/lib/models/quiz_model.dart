import 'dart:convert';
import 'package:flutter/foundation.dart';

class Question {
  final int id;
  final int courseId;
  final int questionSetId;
  final String content;
  final List<Option> options;
  final String explanation;
  final QuestionType type;

  // 新增字段
  final String? answer; // 填空题、简答题的答案
  final List<MatchingItem>? matchingItems; // 匹配题的匹配项
  final List<OrderingItem>? orderingItems; // 排序题的排序项
  final String? caseContent; // 案例题的案例内容
  final String? programmingPrompt; // 编程题的编程要求
  final String? calculationFormula; // 计算题的公式

  Question({
    required this.id,
    required this.courseId,
    required this.questionSetId,
    required this.content,
    required this.options,
    this.explanation = '',
    this.type = QuestionType.singleChoice,
    this.answer,
    this.matchingItems,
    this.orderingItems,
    this.caseContent,
    this.programmingPrompt,
    this.calculationFormula,
  });

  // 从JSON创建问题对象
  factory Question.fromJson(Map<String, dynamic> json) {
    final type = QuestionTypeExtension.fromString(
      json['type'] ?? json['question_type'] ?? 'single_choice',
    );

    // 根据题型解析特定字段
    List<MatchingItem>? matchingItems;
    if (type == QuestionType.matching && json['matching_items'] != null) {
      matchingItems =
          (json['matching_items'] as List)
              .map((item) => MatchingItem.fromJson(item))
              .toList();
    }

    List<OrderingItem>? orderingItems;
    if (type == QuestionType.ordering && json['ordering_items'] != null) {
      orderingItems =
          (json['ordering_items'] as List)
              .map((item) => OrderingItem.fromJson(item))
              .toList();
    }

    return Question(
      id: json['id'] ?? 0,
      courseId: json['course_id'] ?? json['category_level4_id'] ?? 0,
      questionSetId: json['question_set_id'] ?? 0,
      content: json['content'] ?? json['question_text'] ?? '',
      options: _parseOptions(json['options']),
      explanation: json['explanation'] ?? '',
      type: type,
      answer: json['answer']?.toString(),
      matchingItems: matchingItems,
      orderingItems: orderingItems,
      caseContent: json['case_content'],
      programmingPrompt: json['programming_prompt'],
      calculationFormula: json['calculation_formula'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'course_id': courseId,
      'question_set_id': questionSetId,
      'content': content,
      'options': options.map((option) => option.toJson()).toList(),
      'explanation': explanation,
      'type': type.toApiString(),
    };

    // 根据题型添加特定字段
    if (answer != null) data['answer'] = answer;
    if (matchingItems != null) {
      data['matching_items'] =
          matchingItems!.map((item) => item.toJson()).toList();
    }
    if (orderingItems != null) {
      data['ordering_items'] =
          orderingItems!.map((item) => item.toJson()).toList();
    }
    if (caseContent != null) data['case_content'] = caseContent;
    if (programmingPrompt != null) {
      data['programming_prompt'] = programmingPrompt;
    }
    if (calculationFormula != null) {
      data['calculation_formula'] = calculationFormula;
    }

    return data;
  }

  /// 解析options字段，处理字符串和列表两种情况
  static List<Option> _parseOptions(dynamic optionsData) {
    if (optionsData == null) {
      return []; // 某些题型可能没有选项
    }

    // 如果是字符串，尝试解析JSON
    if (optionsData is String) {
      if (optionsData.trim().isEmpty) {
        return [];
      }

      try {
        final parsed = jsonDecode(optionsData);
        if (parsed is List) {
          return _parseOptionsList(parsed);
        } else {
          debugPrint('⚠️ options字符串解析后不是List: $parsed');
          return [];
        }
      } catch (e) {
        debugPrint('❌ options字符串JSON解析失败: $e, 原始数据: $optionsData');
        return [];
      }
    }

    // 如果已经是List，直接解析
    if (optionsData is List) {
      return _parseOptionsList(optionsData);
    }

    debugPrint('⚠️ options数据类型未知: ${optionsData.runtimeType}');
    return [];
  }

  /// 解析选项列表
  static List<Option> _parseOptionsList(List optionsList) {
    return optionsList
        .map((optionData) {
          try {
            // 处理选项数据，既支持字符串也支持对象格式
            if (optionData is String) {
              return Option(
                id: 0,
                content: optionData,
                isCorrect: false,
              );
            } else if (optionData is Map<String, dynamic>) {
              return Option.fromJson(optionData);
            } else {
              debugPrint('⚠️ 未知的选项数据类型: ${optionData.runtimeType}');
              return Option(id: 0, content: '', isCorrect: false);
            }
          } catch (e) {
            debugPrint('❌ 选项解析失败: $e, 数据: $optionData');
            return Option(id: 0, content: '', isCorrect: false);
          }
        })
        .toList();
  }
}

class Option {
  final int id;
  final String content;
  final bool isCorrect;

  Option({required this.id, required this.content, required this.isCorrect});

  // 从JSON创建选项对象
  factory Option.fromJson(Map<String, dynamic> json) {
    return Option(
      id: json['id'],
      content: json['content'],
      isCorrect: json['is_correct'] ?? false,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {'id': id, 'content': content, 'is_correct': isCorrect};
  }
}

// 问题类型枚举
enum QuestionType {
  singleChoice, // 单选题
  multipleChoice, // 多选题
  trueFalse, // 判断题
  fillBlank, // 填空题
  shortAnswer, // 简答题
  matching, // 匹配题
  ordering, // 排序题
  caseAnalysis, // 案例题
  programming, // 编程题
  calculation, // 计算题
}

// 问题类型扩展
extension QuestionTypeExtension on QuestionType {
  // 从字符串转换为问题类型
  static QuestionType fromString(String type) {
    switch (type) {
      case 'multiple_choice':
        return QuestionType.multipleChoice;
      case 'true_false':
        return QuestionType.trueFalse;
      case 'fill_blank':
        return QuestionType.fillBlank;
      case 'short_answer':
        return QuestionType.shortAnswer;
      case 'matching':
        return QuestionType.matching;
      case 'ordering':
        return QuestionType.ordering;
      case 'case_analysis':
        return QuestionType.caseAnalysis;
      case 'programming':
        return QuestionType.programming;
      case 'calculation':
        return QuestionType.calculation;
      case 'single_choice':
      default:
        return QuestionType.singleChoice;
    }
  }

  // 获取题型的显示名称
  String get displayName {
    switch (this) {
      case QuestionType.singleChoice:
        return '单选题';
      case QuestionType.multipleChoice:
        return '多选题';
      case QuestionType.trueFalse:
        return '判断题';
      case QuestionType.fillBlank:
        return '填空题';
      case QuestionType.shortAnswer:
        return '简答题';
      case QuestionType.matching:
        return '匹配题';
      case QuestionType.ordering:
        return '排序题';
      case QuestionType.caseAnalysis:
        return '案例题';
      case QuestionType.programming:
        return '编程题';
      case QuestionType.calculation:
        return '计算题';
    }
  }

  // 转换为后端API使用的字符串
  String toApiString() {
    switch (this) {
      case QuestionType.singleChoice:
        return 'single_choice';
      case QuestionType.multipleChoice:
        return 'multiple_choice';
      case QuestionType.trueFalse:
        return 'true_false';
      case QuestionType.fillBlank:
        return 'fill_blank';
      case QuestionType.shortAnswer:
        return 'short_answer';
      case QuestionType.matching:
        return 'matching';
      case QuestionType.ordering:
        return 'ordering';
      case QuestionType.caseAnalysis:
        return 'case_analysis';
      case QuestionType.programming:
        return 'programming';
      case QuestionType.calculation:
        return 'calculation';
    }
  }
}

// 用户答题记录
class UserAnswer {
  final int questionId;
  final List<int> selectedOptionIds;
  final bool isCorrect;
  final DateTime answeredAt;

  UserAnswer({
    required this.questionId,
    required this.selectedOptionIds,
    required this.isCorrect,
    required this.answeredAt,
  });

  // 从JSON创建用户答题记录
  factory UserAnswer.fromJson(Map<String, dynamic> json) {
    return UserAnswer(
      questionId: json['question_id'],
      selectedOptionIds: List<int>.from(json['selected_option_ids']),
      isCorrect: json['is_correct'] ?? false,
      answeredAt: DateTime.parse(json['answered_at']),
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'question_id': questionId,
      'selected_option_ids': selectedOptionIds,
      'is_correct': isCorrect,
      'answered_at': answeredAt.toIso8601String(),
    };
  }
}

// 答题卡类，表示一套题
class QuizSet {
  final int id;
  final int courseId;
  final String title;
  final List<Question> questions;
  final DateTime createdAt;

  QuizSet({
    required this.id,
    required this.courseId,
    required this.title,
    required this.questions,
    required this.createdAt,
  });

  // 从JSON创建答题卡
  factory QuizSet.fromJson(Map<String, dynamic> json) {
    return QuizSet(
      id: json['id'],
      courseId: json['course_id'],
      title: json['title'],
      questions:
          (json['questions'] as List)
              .map((question) => Question.fromJson(question))
              .toList(),
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'course_id': courseId,
      'title': title,
      'questions': questions.map((question) => question.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
    };
  }
}

// 添加五级分类结构相关模型
class CategoryLevel1 {
  final int id;
  final String name;
  final String description;
  final String highlight;

  CategoryLevel1({
    required this.id,
    required this.name,
    required this.description,
    required this.highlight,
  });

  factory CategoryLevel1.fromJson(Map<String, dynamic> json) {
    return CategoryLevel1(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      highlight: json['highlight'] ?? '',
    );
  }
}

class CategoryLevel2 {
  final int id;
  final String name;
  final String description;
  final String highlight;
  final int parentId;
  final String? englishName; // 添加英文名称字段

  CategoryLevel2({
    required this.id,
    required this.name,
    required this.description,
    required this.highlight,
    required this.parentId,
    this.englishName, // 设为可选参数
  });

  factory CategoryLevel2.fromJson(Map<String, dynamic> json) {
    return CategoryLevel2(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      highlight: json['highlight'] ?? '',
      parentId: json['level1_id'],
      englishName: json['english_name'],
    );
  }
}

class CategoryLevel3 {
  final int id;
  final String name;
  final String description;
  final String highlight;
  final int parentId;
  final String? englishName; // 添加英文名称字段

  CategoryLevel3({
    required this.id,
    required this.name,
    required this.description,
    required this.highlight,
    required this.parentId,
    this.englishName, // 设为可选参数
  });

  factory CategoryLevel3.fromJson(Map<String, dynamic> json) {
    return CategoryLevel3(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      highlight: json['highlight'] ?? '',
      parentId: json['level2_id'],
      englishName: json['english_name'],
    );
  }
}

class CourseItem {
  final int id;
  final String name;
  final String description;
  final String highlight;
  final int parentId;
  final String? englishName; // 添加英文名称字段

  CourseItem({
    required this.id,
    required this.name,
    required this.description,
    required this.highlight,
    required this.parentId,
    this.englishName, // 设为可选参数
  });

  factory CourseItem.fromJson(Map<String, dynamic> json) {
    return CourseItem(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      highlight: json['highlight'] ?? '',
      parentId: json['level3_id'],
      englishName: json['english_name'],
    );
  }
}

// 扩展QuizSet模型，关联到五级分类的CourseItem
class QuestionSet {
  final int id;
  final int courseItemId; // 关联到CourseItem
  final String title;
  final List<Question> questions;
  final DateTime createdAt;

  QuestionSet({
    required this.id,
    required this.courseItemId,
    required this.title,
    required this.questions,
    required this.createdAt,
  });

  // 从JSON创建问题集
  factory QuestionSet.fromJson(Map<String, dynamic> json) {
    return QuestionSet(
      id: json['id'],
      courseItemId: json['course_item_id'],
      title: json['title'],
      questions:
          json['questions'] != null
              ? (json['questions'] as List)
                  .map((question) => Question.fromJson(question))
                  .toList()
              : [],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'course_item_id': courseItemId,
      'title': title,
      'questions': questions.map((question) => question.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  // 按题型过滤题目
  List<Question> getQuestionsByType(QuestionType type) {
    return questions.where((q) => q.type == type).toList();
  }

  // 获取所有可用的题型
  List<QuestionType> getAvailableQuestionTypes() {
    final types = <QuestionType>{};
    for (final question in questions) {
      types.add(question.type);
    }
    return types.toList();
  }

  // 随机抽取指定数量的题目
  List<Question> getRandomQuestions({
    int count = 10,
    List<QuestionType>? types,
  }) {
    if (questions.isEmpty) return [];

    // 如果指定了题型，过滤出符合条件的题目
    List<Question> availableQuestions = questions;
    if (types != null && types.isNotEmpty) {
      availableQuestions =
          questions.where((q) => types.contains(q.type)).toList();
    }

    if (availableQuestions.isEmpty) return [];

    // 如果题目数量不足，返回全部
    if (availableQuestions.length <= count) {
      return List.from(availableQuestions);
    }

    // 随机抽取
    availableQuestions.shuffle();
    return availableQuestions.take(count).toList();
  }

  // 获取按题型分组的题目
  Map<QuestionType, List<Question>> getQuestionsByTypeMap() {
    final Map<QuestionType, List<Question>> result = {};

    for (final question in questions) {
      if (!result.containsKey(question.type)) {
        result[question.type] = [];
      }
      result[question.type]!.add(question);
    }

    return result;
  }
}

// 匹配题的匹配项
class MatchingItem {
  final int id;
  final String left; // 左侧选项
  final String right; // 右侧匹配项

  MatchingItem({required this.id, required this.left, required this.right});

  factory MatchingItem.fromJson(Map<String, dynamic> json) {
    return MatchingItem(
      id: json['id'],
      left: json['left'],
      right: json['right'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'left': left, 'right': right};
  }
}

// 排序题的排序项
class OrderingItem {
  final int id;
  final String content; // 排序项内容
  final int correctOrder; // 正确顺序

  OrderingItem({
    required this.id,
    required this.content,
    required this.correctOrder,
  });

  factory OrderingItem.fromJson(Map<String, dynamic> json) {
    return OrderingItem(
      id: json['id'],
      content: json['content'],
      correctOrder: json['correct_order'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'content': content, 'correct_order': correctOrder};
  }
}

// 五级分类标签页
class CategoryTab {
  final int id;
  final String name;
  final String description;
  final int level4Id;
  final bool isActive;
  final String tabType;
  final String? englishName; // 添加英文名称字段

  CategoryTab({
    required this.id,
    required this.name,
    required this.description,
    required this.level4Id,
    required this.isActive,
    required this.tabType,
    this.englishName, // 设为可选参数
  });

  factory CategoryTab.fromJson(Map<String, dynamic> json) {
    debugPrint("解析标签数据: $json"); // 添加日志
    return CategoryTab(
      id: json['id'] ?? 0, // 添加默认值
      name: json['name'] ?? '未命名标签',
      description: json['description'] ?? '',
      level4Id: json['level4_id'] ?? 0,
      isActive: json['is_active'] ?? true,
      tabType: json['tab_type'] ?? 'quiz',
      englishName: json['english_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'level4_id': level4Id,
      'is_active': isActive,
      'tab_type': tabType,
      'english_name': englishName,
    };
  }
}
