
class ArticleCategory {
  final String id;
  final String name;
  final String imageUrl;

  ArticleCategory({required this.id, required this.name, this.imageUrl = ''});

  factory ArticleCategory.fromJson(Map<String, dynamic> json) {
    return ArticleCategory(
      id: json['id'],
      name: json['name'],
      imageUrl: json['image_url'] ?? '',
    );
  }
}

class Article {
  final String id;
  final String title;
  final String subtitle;
  final String content;
  final DateTime publishDate;
  final String thumbnailUrl;
  final String authorName;
  final String source;
  final List<String> tags;
  final List<String> categoryIds;
  final bool isFeatured;
  final int readTimeMinutes;

  Article({
    required this.id,
    required this.title,
    this.subtitle = '',
    required this.content,
    required this.publishDate,
    this.thumbnailUrl = '',
    this.authorName = '',
    this.source = '',
    this.tags = const [],
    this.categoryIds = const [],
    this.isFeatured = false,
    this.readTimeMinutes = 0,
  });

  factory Article.fromJson(Map<String, dynamic> json) {
    return Article(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'] ?? '',
      content: json['content'],
      publishDate: DateTime.parse(json['publish_date']),
      thumbnailUrl: json['thumbnail_url'] ?? '',
      authorName: json['author_name'] ?? '',
      source: json['source'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      categoryIds: List<String>.from(json['category_ids'] ?? []),
      isFeatured: json['is_featured'] ?? false,
      readTimeMinutes: json['read_time_minutes'] ?? 0,
    );
  }

  String get formattedDate {
    final day = publishDate.day.toString().padLeft(2, '0');
    final month = publishDate.month.toString().padLeft(2, '0');
    final year = publishDate.year.toString();
    return '$year-$month-$day';
  }

  String get shortContent {
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }
}

class ArticleCollection {
  final String id;
  final String title;
  final String description;
  final List<Article> articles;
  final String imageUrl;

  ArticleCollection({
    required this.id,
    required this.title,
    this.description = '',
    required this.articles,
    this.imageUrl = '',
  });

  factory ArticleCollection.fromJson(Map<String, dynamic> json) {
    List<Article> articlesList = [];
    if (json['articles'] != null) {
      articlesList = List<Article>.from(
        json['articles'].map((x) => Article.fromJson(x)),
      );
    }

    return ArticleCollection(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      articles: articlesList,
      imageUrl: json['image_url'] ?? '',
    );
  }
}
