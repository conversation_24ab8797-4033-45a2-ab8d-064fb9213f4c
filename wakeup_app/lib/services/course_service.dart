import 'api_service.dart';
import 'user_service.dart';

/// 课程服务类，处理获取课程和课程类别相关的API请求
class CourseService extends ApiService {
  /// 单例模式
  static final CourseService _instance = CourseService._internal();
  
  factory CourseService() {
    return _instance;
  }
  
  CourseService._internal();
  
  /// 用户服务实例
  final UserService _userService = UserService();
  
  /// 获取所有课程列表
  Future<List<Map<String, dynamic>>> getAllCourses() async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/courses', token: token, expectList: true);
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取课程详情
  /// [courseId] 课程ID
  Future<Map<String, dynamic>> getCourseDetails(int courseId) async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/courses/$courseId', token: token, expectList: false);
    return response;
  }
  
  /// 获取用户已购买的课程
  Future<List<Map<String, dynamic>>> getUserCourses() async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/user/courses', token: token, expectList: true);
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取课程的一级类别列表
  /// [courseId] 课程ID
  Future<List<Map<String, dynamic>>> getCourseCategories(int courseId) async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/courses/$courseId/categories', token: token, expectList: true);
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取四级类别列表
  /// [level3Id] 三级类别ID
  Future<List<Map<String, dynamic>>> getLevel4Categories(int level3Id) async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/categories/level4',
      token: token,
      queryParams: {
        'parent_id': level3Id.toString()
      },
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取五级类别列表
  /// [level4Id] 四级类别ID
  Future<List<Map<String, dynamic>>> getLevel5Categories(int level4Id) async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/categories/level5',
      token: token,
      queryParams: {
        'parent_id': level4Id.toString()
      },
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取最近学习的课程
  /// [limit] 限制返回数量，默认为5
  Future<List<Map<String, dynamic>>> getRecentCourses({int limit = 5}) async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/user/recent_courses',
      token: token,
      queryParams: {
        'limit': limit.toString()
      },
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取课程的学习进度
  /// [courseId] 课程ID
  Future<Map<String, dynamic>> getCourseProgress(int courseId) async {
    final token = await _userService.getAuthToken();
    final response = await get('/api/user/course_progress',
      token: token,
      queryParams: {
        'course_id': courseId.toString()
      },
      expectList: false
    );
    return response;
  }
} 