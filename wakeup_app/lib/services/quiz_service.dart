import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/quiz_model.dart';
import 'auth_service.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../utils/api_utils.dart';
import 'package:wakeup_app/core/utils/logger.dart';

class QuizService {
  // 获取用户已加入的课程列表
  static Future<List<CourseItem>> getUserCourses(
    int userId,
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/user_course/list/$userId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => CourseItem.fromJson(json)).toList();
      } else {
        throw Exception('获取用户课程失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取用户课程出错: $e');
    }
  }

  // 获取课程对应的题库列表
  static Future<List<QuestionSet>> getQuestionSets(
    int courseItemId,
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/quiz/sets/$courseItemId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => QuestionSet.fromJson(json)).toList();
      } else {
        throw Exception('获取题库列表失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取题库列表出错: $e');
    }
  }

  // 获取题库中的问题列表，可按题型过滤
  static Future<List<Question>> getQuestionsBySet(
    int questionSetId,
    String token, {
    List<QuestionType>? types,
    int? limit,
    bool randomize = false,
  }) async {
    try {
      String url =
          '${AuthService.baseUrl}/api/quiz/questions/set/$questionSetId';

      // 添加查询参数
      final queryParams = <String>[];
      if (types != null && types.isNotEmpty) {
        final typeParams = types.map((t) => t.toApiString()).join(',');
        queryParams.add('types=$typeParams');
      }
      if (limit != null) {
        queryParams.add('limit=$limit');
      }
      if (randomize) {
        queryParams.add('random=true');
      }

      if (queryParams.isNotEmpty) {
        url += '?${queryParams.join('&')}';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Question.fromJson(json)).toList();
      } else {
        throw Exception('获取题库问题失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取题库问题出错: $e');
    }
  }

  // 获取课程问题列表 (保留兼容性)
  static Future<List<Question>> getQuestions(int courseId, String token) async {
    try {
      debugPrint("🔍 正在获取题目: courseId=$courseId");
      final url = ApiUtils.buildUrl('/api/courses/$courseId/questions');

      final response = await http
          .get(Uri.parse(url), headers: ApiUtils.buildHeaders(token))
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        debugPrint("✅ 获取题目成功: ${jsonData['data'].length}道题目");

        final List<Question> questions =
            (jsonData['data'] as List)
                .map(
                  (item) => Question(
                    id: item['id'] ?? 0,
                    courseId: courseId,
                    questionSetId: 0,
                    content: item['question_text'] ?? "题目内容缺失",
                    options: _parseOptions(item['options']),
                    explanation: item['explanation'] ?? "暂无解析",
                    answer: item['answer']?.toString(),
                    type: _parseQuestionType(item['question_type']),
                  ),
                )
                .toList();

        return questions;
      } else {
        final errorMessage = "获取题目失败: HTTP ${response.statusCode}";
        debugPrint("❌ $errorMessage");
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint("❌ 获取题目时出错: $e");
      throw Exception('获取题目失败: $e');
    }
  }

  // 解析题目选项
  static List<Option> _parseOptions(dynamic optionsData) {
    if (optionsData == null) return [];

    try {
      List<dynamic> optionsList;
      if (optionsData is String) {
        optionsList = jsonDecode(optionsData);
      } else if (optionsData is List) {
        optionsList = optionsData;
      } else {
        return [];
      }

      return optionsList.asMap().entries.map((entry) {
        int index = entry.key;
        String content = entry.value.toString();
        return Option(
          id: index + 1,
          content: content,
          isCorrect: false, // 由后端answer字段决定
        );
      }).toList();
    } catch (e) {
      debugPrint("❌ 解析选项失败: $e");
      return [];
    }
  }

  // 解析题目类型
  static QuestionType _parseQuestionType(dynamic typeData) {
    String? typeString;

    // 处理不同的数据类型
    if (typeData is String) {
      typeString = typeData;
    } else if (typeData is int) {
      // 如果是整数，转换为对应的字符串
      switch (typeData) {
        case 0:
        case 1:
          typeString = 'single_choice';
          break;
        case 2:
          typeString = 'multiple_choice';
          break;
        case 3:
          typeString = 'true_false';
          break;
        case 4:
          typeString = 'fill_blank';
          break;
        default:
          typeString = 'single_choice';
      }
    } else {
      debugPrint(
        "⚠️ 未知的question_type类型: ${typeData?.runtimeType}, 值: $typeData",
      );
      typeString = 'single_choice';
    }

    switch (typeString) {
      case 'multiple_choice':
      case 'single_choice':
        return QuestionType.singleChoice;
      case 'true_false':
        return QuestionType.trueFalse;
      case 'fill_blank':
        return QuestionType.fillBlank;
      default:
        return QuestionType.singleChoice;
    }
  }

  // 获取完整题库 (保留兼容性)
  static Future<QuestionSet> getQuizSet(int courseId, String token) async {
    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/quiz/set/course/$courseId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        return QuestionSet.fromJson(data);
      } else {
        throw Exception('获取问题集失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取问题集出错: $e');
    }
  }

  // 提交答案
  static Future<bool> submitAnswer(
    int questionId,
    String userAnswer,
    bool isCorrect,
    String token,
  ) async {
    try {
      debugPrint("📝 正在提交答题结果: questionId=$questionId, isCorrect=$isCorrect");
      final url = ApiUtils.buildUrl('/api/questions/$questionId/answers');

      final Map<String, dynamic> data = {
        'user_answer': userAnswer,
        'is_correct': isCorrect,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await http
          .post(
            Uri.parse(url),
            headers: ApiUtils.buildHeaders(token),
            body: jsonEncode(data),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint("✅ 提交答题结果成功");
        return true;
      } else {
        debugPrint("❌ 提交答题结果失败: HTTP ${response.statusCode}");
        return false;
      }
    } catch (e) {
      debugPrint("❌ 提交答题结果时出错: $e");
      return false;
    }
  }

  // 获取用户的错题本
  static Future<List<Question>> getWrongQuestions(
    int userId,
    int courseId,
    String token, {
    int? questionSetId,
  }) async {
    try {
      String url =
          '${AuthService.baseUrl}/api/quiz/wrong-questions/$userId/$courseId';
      if (questionSetId != null) {
        url += '?question_set_id=$questionSetId';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Question.fromJson(json)).toList();
      } else {
        throw Exception('获取错题失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取错题出错: $e');
    }
  }

  // 获取用户的答题统计
  static Future<Map<String, dynamic>> getQuizStats(
    int userId,
    int courseId,
    String token, {
    int? questionSetId,
  }) async {
    try {
      String url = '${AuthService.baseUrl}/api/quiz/stats/$userId/$courseId';
      if (questionSetId != null) {
        url += '?question_set_id=$questionSetId';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('获取统计信息失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取统计信息出错: $e');
    }
  }

  // 获取五级分类结构
  static Future<List<CategoryLevel1>> getCategoryLevel1(String token) async {
    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/categories/level1'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => CategoryLevel1.fromJson(json)).toList();
      } else {
        throw Exception('获取一级分类失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取一级分类出错: $e');
    }
  }

  static Future<List<CategoryLevel2>> getCategoryLevel2(
    int parentId,
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${AuthService.baseUrl}/api/categories/level2?parent_id=$parentId',
        ),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => CategoryLevel2.fromJson(json)).toList();
      } else {
        throw Exception('获取二级分类失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取二级分类出错: $e');
    }
  }

  static Future<List<CategoryLevel3>> getCategoryLevel3(
    int parentId,
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${AuthService.baseUrl}/api/categories/level3?parent_id=$parentId&_t=${DateTime.now().millisecondsSinceEpoch}',
        ),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => CategoryLevel3.fromJson(json)).toList();
      } else {
        throw Exception('获取三级分类失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取三级分类出错: $e');
    }
  }

  static Future<List<CourseItem>> getCourseItems(
    int parentId,
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${AuthService.baseUrl}/api/categories/level4?parent_id=$parentId&_t=${DateTime.now().millisecondsSinceEpoch}',
        ),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => CourseItem.fromJson(json)).toList();
      } else {
        throw Exception('获取课程项目失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取课程项目出错: $e');
    }
  }

  // 获取题型统计信息
  static Future<Map<String, int>> getQuestionTypeStats(
    int questionSetId,
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/quiz/type-stats/$questionSetId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);

        // 将字符串类型转换为整数
        final Map<String, int> result = {};
        data.forEach((key, value) {
          if (value is int) {
            result[key] = value;
          } else if (value is String) {
            result[key] = int.tryParse(value) ?? 0;
          }
        });

        return result;
      } else {
        throw Exception('获取题型统计失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('获取题型统计出错: $e');
    }
  }

  // 生成测试题目集
  static Future<List<Question>> generateQuiz(
    int questionSetId,
    String token, {
    required Map<QuestionType, int> typeDistribution,
    int totalQuestions = 20,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AuthService.baseUrl}/api/quiz/generate'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'question_set_id': questionSetId,
          'total_questions': totalQuestions,
          'type_distribution': typeDistribution.map(
            (type, count) => MapEntry(type.toApiString(), count),
          ),
        }),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Question.fromJson(json)).toList();
      } else {
        throw Exception('生成测试题目失败: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('生成测试题目出错: $e');
    }
  }

  // 本地生成随机题目集（当后端API不可用时的备选方案）
  static List<Question> generateRandomQuizLocally(
    List<Question> allQuestions, {
    required Map<QuestionType, int> typeDistribution,
    int totalQuestions = 20,
  }) {
    // 按题型分组
    final Map<QuestionType, List<Question>> questionsByType = {};
    for (final question in allQuestions) {
      if (!questionsByType.containsKey(question.type)) {
        questionsByType[question.type] = [];
      }
      questionsByType[question.type]!.add(question);
    }

    // 生成题目集
    final List<Question> result = [];

    // 按分布选择题目
    typeDistribution.forEach((type, count) {
      if (questionsByType.containsKey(type) &&
          questionsByType[type]!.isNotEmpty) {
        final availableQuestions = List<Question>.from(questionsByType[type]!);

        // 随机打乱
        availableQuestions.shuffle();

        // 取需要的数量
        final actualCount = min(count, availableQuestions.length);
        result.addAll(availableQuestions.take(actualCount));
      }
    });

    // 如果生成的题目不足，从剩余题目中随机补充
    if (result.length < totalQuestions) {
      final remainingQuestions =
          allQuestions.where((q) => !result.contains(q)).toList();
      remainingQuestions.shuffle();

      final needCount = totalQuestions - result.length;
      final additionalCount = min(needCount, remainingQuestions.length);

      result.addAll(remainingQuestions.take(additionalCount));
    }

    // 再次随机打乱
    result.shuffle();

    return result;
  }

  // 获取课程分类标签
  static Future<List<CategoryTab>> getCategoryTabs(
    int courseId,
    String token,
  ) async {
    try {
      debugPrint("🔍 正在获取课程分类标签: courseId=$courseId");
      final url = ApiUtils.buildUrl('/api/course_tabs?course_id=$courseId');

      final response = await http
          .get(Uri.parse(url), headers: ApiUtils.buildHeaders(token))
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        debugPrint("✅ 获取分类标签成功: ${jsonData['data'].length}个标签");

        // 将JSON数据转换为CategoryTab对象列表
        final List<CategoryTab> tabs =
            (jsonData['data'] as List)
                .map(
                  (item) => CategoryTab(
                    id: item['id'] ?? 0,
                    name: item['name'] ?? "未命名",
                    description: item['description'],
                    level4Id: item['level4_id'] ?? courseId,
                    isActive: item['is_active'] ?? true,
                    tabType: item['tab_type'] ?? "unknown",
                    englishName: item['english_name'],
                  ),
                )
                .toList();

        return tabs;
      } else {
        final errorMessage = "获取分类标签失败: HTTP ${response.statusCode}";
        debugPrint("❌ $errorMessage");
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint("❌ 获取分类标签时出错: $e");
      throw Exception('获取分类标签失败: $e');
    }
  }

  // 生成模拟分类标签数据
  static List<CategoryTab> _generateMockCategoryTabs(int courseId) { // ignore: unused_element
    return [
      CategoryTab(
        id: 0,
        name: "答题练习",
        description: "练习答题",
        level4Id: courseId,
        isActive: true,
        tabType: "quiz",
        englishName: "Quiz",
      ),
      CategoryTab(
        id: 1,
        name: "错题本",
        description: "查看错题",
        level4Id: courseId,
        isActive: true,
        tabType: "wrong_questions",
        englishName: "Wrong Questions",
      ),
      CategoryTab(
        id: 2,
        name: "数据面板",
        description: "答题统计",
        level4Id: courseId,
        isActive: true,
        tabType: "stats",
        englishName: "Statistics",
      ),
    ];
  }

  // 获取标签页内容(第6级内容)
  static Future<List<QuestionSet>> getLevel6Content(
    int tabId,
    String token, {
    String contentType = 'quiz',
  }) async {
    try {
      debugPrint("🔍 正在获取标签内容: tabId=$tabId, type=$contentType");
      final url = ApiUtils.buildUrl(
        '/api/tab_content?tab_id=$tabId&type=$contentType',
      );

      final response = await http
          .get(Uri.parse(url), headers: ApiUtils.buildHeaders(token))
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        debugPrint("✅ 获取标签内容成功: ${jsonData['data'].length}个条目");

        // 根据内容类型处理数据
        if (contentType == 'quiz') {
          final List<QuestionSet> questionSets =
              (jsonData['data'] as List)
                  .map(
                    (item) => QuestionSet(
                      id: item['id'] ?? 0,
                      courseItemId: item['course_item_id'] ?? tabId,
                      title: item['name'] ?? item['title'] ?? "未命名题库",
                      questions: [],
                      createdAt: DateTime.now(),
                    ),
                  )
                  .toList();

          return questionSets;
        } else {
          // 其他类型内容的处理...
          return [];
        }
      } else {
        final errorMessage = "获取标签内容失败: HTTP ${response.statusCode}";
        debugPrint("❌ $errorMessage");
        throw Exception(errorMessage);
      }
    } catch (e) {
      debugPrint("❌ 获取标签内容时出错: $e");
      throw Exception('获取标签内容失败: $e');
    }
  }

  // 获取六级考试题库数据
  static Future<List<Question>> getCet6Questions(
    String token, {
    int limit = 20,
  }) async {
    try {
      final response = await http.get(
        Uri.parse(
          '${AuthService.baseUrl}/api/quiz/cet6/questions?limit=$limit',
        ),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => Question.fromJson(json)).toList();
      } else {
        throw Exception('获取六级题库失败: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('获取六级题库出错', error: e);
      throw Exception('获取六级题库失败: $e');
    }
  }
}
