import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/knowledge_node_model.dart';
import '../services/auth_service.dart';
import '../core/utils/logger.dart';

/// 思维导图服务
///
/// 负责与后端AI API交互，获取知识节点数据
class MindMapService {
  static final MindMapService _instance = MindMapService._internal();
  factory MindMapService() => _instance;
  MindMapService._internal();

  /// HTTP客户端
  final http.Client _client = http.Client();

  /// 获取知识分支
  ///
  /// 调用后端AI API获取指定主题的知识分支
  ///
  /// 参数：
  /// - topic: 主题内容
  ///
  /// 返回：知识节点数据
  ///
  /// 异常：网络错误或API错误时抛出异常
  Future<KnowledgeNode> getBranches(String topic) async {
    try {
      AppLogger.info('正在获取知识分支: $topic');

      final url = Uri.parse('${AuthService.baseUrl}/api/ai/branches');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      final body = jsonEncode({'topic': topic});

      AppLogger.network('POST', '/api/ai/branches');

      final response = await _client
          .post(url, headers: headers, body: body)
          .timeout(const Duration(seconds: 30));

      AppLogger.info('AI API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        AppLogger.success('成功获取知识分支数据');
        AppLogger.info('API响应数据: $data');

        return KnowledgeNode.fromApiResponse(data);
      } else {
        final errorMessage = '获取知识分支失败: HTTP ${response.statusCode}';
        AppLogger.error(errorMessage);

        // 尝试解析错误响应
        try {
          final errorData = jsonDecode(response.body);
          final apiError = errorData['error'] ?? errorMessage;
          throw Exception(apiError);
        } catch (e) {
          throw Exception(errorMessage);
        }
      }
    } catch (e) {
      AppLogger.error('获取知识分支时发生错误', error: e);

      // 如果是网络错误，返回模拟数据以保证用户体验
      if (e.toString().contains('timeout') ||
          e.toString().contains('network') ||
          e.toString().contains('connection')) {
        AppLogger.warning('网络错误，返回模拟数据');
        return _getMockBranches(topic);
      }

      rethrow;
    }
  }

  /// 获取问题答案
  ///
  /// 调用后端AI API获取指定问题的答案
  ///
  /// 参数：
  /// - topic: 上下文主题
  /// - question: 具体问题
  ///
  /// 返回：问题的答案内容
  ///
  /// 异常：网络错误或API错误时抛出异常
  Future<String> getAnswer(String topic, String question) async {
    try {
      AppLogger.info('正在获取问题答案: $question (主题: $topic)');

      final url = Uri.parse('${AuthService.baseUrl}/api/ai/ask');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      final body = jsonEncode({'topic': topic, 'question': question});

      AppLogger.network('POST', '/api/ai/ask');

      final response = await _client
          .post(url, headers: headers, body: body)
          .timeout(const Duration(seconds: 30));

      AppLogger.info('AI API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        final answer = data['answer'] ?? '暂无答案';

        AppLogger.success('成功获取问题答案');
        AppLogger.info('答案内容: $answer');

        return answer;
      } else {
        final errorMessage = '获取问题答案失败: HTTP ${response.statusCode}';
        AppLogger.error(errorMessage);

        // 尝试解析错误响应
        try {
          final errorData = jsonDecode(response.body);
          final apiError = errorData['error'] ?? errorMessage;
          throw Exception(apiError);
        } catch (e) {
          throw Exception(errorMessage);
        }
      }
    } catch (e) {
      AppLogger.error('获取问题答案时发生错误', error: e);

      // 如果是网络错误，返回模拟答案
      if (e.toString().contains('timeout') ||
          e.toString().contains('network') ||
          e.toString().contains('connection')) {
        AppLogger.warning('网络错误，返回模拟答案');
        return _getMockAnswer(topic, question);
      }

      rethrow;
    }
  }

  /// 展开知识节点
  ///
  /// 为指定的知识节点获取子节点内容
  ///
  /// 参数：
  /// - node: 要展开的知识节点
  ///
  /// 返回：更新后的知识节点（包含子节点内容）
  Future<KnowledgeNode> expandNode(KnowledgeNode node) async {
    try {
      AppLogger.info('正在展开知识节点: ${node.title}');

      // 获取节点的详细内容
      final answer = await getAnswer(node.title, node.title);

      // 获取子节点的分支
      final branchData = await getBranches(node.title);

      // 更新节点内容和子节点
      final updatedNode = node.copyWith(
        content: answer,
        isExpanded: true,
        children:
            branchData.children.map((child) {
              return child.copyWith(level: node.level + 1, parentId: node.id);
            }).toList(),
      );

      AppLogger.success('成功展开知识节点: ${node.title}');
      return updatedNode;
    } catch (e) {
      AppLogger.error('展开知识节点失败', error: e);
      rethrow;
    }
  }

  /// 获取模拟知识分支数据
  ///
  /// 当网络请求失败时提供备用数据
  ///
  /// 参数：
  /// - topic: 主题内容
  ///
  /// 返回：模拟的知识节点数据
  KnowledgeNode _getMockBranches(String topic) {
    final mockData = {
      'title': topic,
      'content': '$topic 是一个重要的知识领域，涉及多个方面的内容。通过深入学习可以掌握其核心概念和应用方法。',
      'children': ['$topic 的基础概念是什么？', '$topic 有哪些实际应用？', '如何深入学习 $topic？'],
    };

    return KnowledgeNode.fromApiResponse(mockData);
  }

  /// 获取模拟问题答案
  ///
  /// 当网络请求失败时提供备用答案
  ///
  /// 参数：
  /// - topic: 上下文主题
  /// - question: 具体问题
  ///
  /// 返回：模拟的答案内容
  String _getMockAnswer(String topic, String question) {
    return '关于"$question"的回答：这是一个关于 $topic 的重要问题。'
        '建议通过系统学习和实践来深入理解相关概念。'
        '您可以从基础知识开始，逐步深入到高级应用。';
  }

  /// 释放资源
  ///
  /// 关闭HTTP客户端，释放相关资源
  void dispose() {
    _client.close();
    AppLogger.info('MindMapService 资源已释放');
  }
}
