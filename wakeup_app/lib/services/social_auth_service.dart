import 'dart:convert';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:google_sign_in/google_sign_in.dart';
// import 'package:fluwx/fluwx.dart';
// import 'package:tencent_kit/tencent_kit.dart';
import 'package:http/http.dart' as http;
import 'auth_service.dart';
import 'package:wakeup_app/core/utils/logger.dart';

/// 社交登录提供商枚举
enum SocialProvider {
  apple('apple', 'Apple'),
  wechat('wechat', '微信'),
  qq('qq', 'QQ'),
  google('google', 'Google');

  const SocialProvider(this.id, this.displayName);
  final String id;
  final String displayName;
}

/// 社交登录用户信息
class SocialUserInfo {
  final String providerId;
  final String openId;
  final String? unionId;
  final String? email;
  final String? displayName;
  final String? avatarUrl;
  final String? accessToken;
  final String? refreshToken;

  const SocialUserInfo({
    required this.providerId,
    required this.openId,
    this.unionId,
    this.email,
    this.displayName,
    this.avatarUrl,
    this.accessToken,
    this.refreshToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'provider_id': providerId,
      'open_id': openId,
      'union_id': unionId,
      'email': email,
      'display_name': displayName,
      'avatar_url': avatarUrl,
      'access_token': accessToken,
      'refresh_token': refreshToken,
    };
  }
}

/// 社交登录服务类
class SocialAuthService {
  static const String _googleClientId = 'YOUR_GOOGLE_CLIENT_ID';
  static const String _qqAppId = 'YOUR_QQ_APP_ID'; // ignore: unused_field
  static const String _wechatAppId = 'YOUR_WECHAT_APP_ID'; // ignore: unused_field

  static GoogleSignIn? _googleSignIn;
  static bool _isWechatInitialized = false;
  static bool _isQQInitialized = false;

  /// 初始化社交登录服务
  static Future<void> initialize() async {
    try {
      // 初始化Google登录
      _googleSignIn = GoogleSignIn(
        clientId: _googleClientId,
        scopes: ['email', 'profile'],
      );

      // 初始化微信
      await _initializeWechat();

      // 初始化QQ
      await _initializeQQ();
    } catch (e) {
      AppLogger.error('社交登录初始化失败', error: e);
    }
  }

  /// 初始化微信SDK
  static Future<void> _initializeWechat() async {
    try {
      // TODO: 恢复微信SDK集成
      // await fluwx.registerWxApi(
      //   appId: _wechatAppId,
      //   doOnAndroid: true,
      //   doOnIOS: true,
      // );
      _isWechatInitialized = false; // 临时禁用
    } catch (e) {
      AppLogger.error('微信SDK初始化失败', error: e);
      _isWechatInitialized = false;
    }
  }

  /// 初始化QQ SDK
  static Future<void> _initializeQQ() async {
    try {
      // TODO: 恢复QQ SDK集成
      // await TencentKit.registerApp(appId: _qqAppId);
      _isQQInitialized = false; // 临时禁用
    } catch (e) {
      AppLogger.error('QQ SDK初始化失败', error: e);
      _isQQInitialized = false;
    }
  }

  /// Apple登录
  static Future<SocialUserInfo?> signInWithApple() async {
    try {
      // 检查Apple登录可用性
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        throw Exception('当前设备不支持Apple登录');
      }

      // 请求授权
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // 构建用户信息
      String? displayName;
      if (credential.givenName != null || credential.familyName != null) {
        displayName =
            '${credential.familyName ?? ''}${credential.givenName ?? ''}';
        if (displayName.trim().isEmpty) displayName = null;
      }

      return SocialUserInfo(
        providerId: SocialProvider.apple.id,
        openId: credential.userIdentifier ?? '',
        email: credential.email,
        displayName: displayName,
        accessToken: credential.identityToken,
      );
    } on SignInWithAppleAuthorizationException catch (e) {
      switch (e.code) {
        case AuthorizationErrorCode.canceled:
          throw Exception('用户取消了Apple登录');
        case AuthorizationErrorCode.failed:
          throw Exception('Apple登录失败');
        case AuthorizationErrorCode.invalidResponse:
          throw Exception('Apple登录响应无效');
        case AuthorizationErrorCode.notHandled:
          throw Exception('Apple登录未处理');
        case AuthorizationErrorCode.unknown:
        default:
          throw Exception('Apple登录发生未知错误');
      }
    } catch (e) {
      throw Exception('Apple登录失败: $e');
    }
  }

  /// 微信登录
  static Future<SocialUserInfo?> signInWithWechat() async {
    try {
      if (!_isWechatInitialized) {
        await _initializeWechat();
        if (!_isWechatInitialized) {
          throw Exception('微信SDK未初始化');
        }
      }

      // TODO: 恢复微信SDK集成后实现
      throw UnimplementedError('微信登录暂时不可用，需要修复SDK集成问题');
    } catch (e) {
      throw Exception('微信登录失败: $e');
    }
  }

  /// QQ登录
  static Future<SocialUserInfo?> signInWithQQ() async {
    try {
      if (!_isQQInitialized) {
        await _initializeQQ();
        if (!_isQQInitialized) {
          throw Exception('QQ SDK未初始化');
        }
      }

      // TODO: 恢复QQ SDK集成后实现
      throw UnimplementedError('QQ登录暂时不可用，需要修复SDK集成问题');
    } catch (e) {
      throw Exception('QQ登录失败: $e');
    }
  }

  /// Google登录
  static Future<SocialUserInfo?> signInWithGoogle() async {
    try {
      if (_googleSignIn == null) {
        throw Exception('Google登录未初始化');
      }

      // 尝试静默登录
      GoogleSignInAccount? account = await _googleSignIn!.signInSilently();

      // 如果静默登录失败，进行交互式登录
      account ??= await _googleSignIn!.signIn();

      if (account == null) {
        throw Exception('用户取消了Google登录');
      }

      // 获取认证信息
      final auth = await account.authentication;

      return SocialUserInfo(
        providerId: SocialProvider.google.id,
        openId: account.id,
        email: account.email,
        displayName: account.displayName,
        avatarUrl: account.photoUrl,
        accessToken: auth.accessToken,
        refreshToken: auth.idToken,
      );
    } catch (e) {
      throw Exception('Google登录失败: $e');
    }
  }

  /// 统一社交登录入口
  static Future<SocialUserInfo?> signInWith(SocialProvider provider) async {
    switch (provider) {
      case SocialProvider.apple:
        return await signInWithApple();
      case SocialProvider.wechat:
        return await signInWithWechat();
      case SocialProvider.qq:
        return await signInWithQQ();
      case SocialProvider.google:
        return await signInWithGoogle();
    }
  }

  /// 社交登录（首次登录即注册）
  static Future<Map<String, dynamic>> loginWithSocial(
    SocialUserInfo userInfo,
  ) async {
    try {
      final url = Uri.parse('${AuthService.baseUrl}/api/auth/oauth_login');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode(userInfo.toJson()),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = json.decode(response.body);

        // 保存登录状态
        if (data.containsKey('token')) {
          // 这里可以保存token到本地存储
          // 例如：await StorageService.instance.setAuthToken(data['token']);
        }

        return data;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? '社交登录失败');
      }
    } catch (e) {
      throw Exception('登录失败: $e');
    }
  }

  /// 完整的社交登录流程
  static Future<Map<String, dynamic>> performSocialLogin(
    SocialProvider provider,
  ) async {
    try {
      // 1. 执行社交登录
      final userInfo = await signInWith(provider);
      if (userInfo == null) {
        throw Exception('社交登录取消或失败');
      }

      // 2. 向后端验证并完成登录/注册
      final result = await loginWithSocial(userInfo);

      // 3. 返回结果
      return result;
    } catch (e) {
      throw Exception('社交登录流程失败: $e');
    }
  }

  /// 登出所有社交账号
  static Future<void> signOutAll() async {
    try {
      // Google登出
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }

      // 微信登出 (微信没有显式登出接口)

      // QQ登出
      // TODO: 恢复QQ SDK集成后实现
      // if (_isQQInitialized) {
      //   await TencentKit.logout();
      // }
    } catch (e) {
      AppLogger.error('社交登录登出失败', error: e);
    }
  }

  /// 检查社交应用是否可用
  static Future<Map<SocialProvider, bool>> checkAvailability() async {
    final Map<SocialProvider, bool> availability = {};

    // Apple登录可用性
    try {
      availability[SocialProvider.apple] = await SignInWithApple.isAvailable();
    } catch (e) {
      availability[SocialProvider.apple] = false;
    }

    // 微信可用性
    try {
      // TODO: 恢复微信SDK集成后实现
      availability[SocialProvider.wechat] = false; // 临时禁用
    } catch (e) {
      availability[SocialProvider.wechat] = false;
    }

    // QQ可用性
    try {
      // TODO: 恢复QQ SDK集成后实现
      availability[SocialProvider.qq] = false; // 临时禁用
    } catch (e) {
      availability[SocialProvider.qq] = false;
    }

    // Google登录 (总是可用，使用WebView)
    availability[SocialProvider.google] = true;

    return availability;
  }
}
