import 'package:flutter/material.dart';
import 'widgets/gradient_course_card.dart';

class TestGradientCards extends StatelessWidget {
  const TestGradientCards({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('极光渐变卡片测试'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            const Text(
              '使用 Mesh 网格渐变的极光效果',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // 紫色极光系
            GradientCourseCard(
              courseName: 'JavaScript基础',
              description: 'Web前端开发技术',
              highlight: '推荐学习',
              categoryId: 0, // 紫色极光
              onTap: () {},
            ),
            const SizedBox(height: 16),
            
            // 蓝色极光系
            GradientCourseCard(
              courseName: 'Python编程',
              description: '人工智能与数据科学',
              highlight: '热门课程',
              categoryId: 1, // 蓝色极光
              onTap: () {},
            ),
            const SizedBox(height: 16),
            
            // 绿色极光系
            GradientCourseCard(
              courseName: 'Node.js后端',
              description: '服务器端开发技术',
              highlight: '进阶课程',
              categoryId: 2, // 绿色极光
              onTap: () {},
            ),
            const SizedBox(height: 16),
            
            // 橙色极光系
            GradientCourseCard(
              courseName: 'React框架',
              description: '现代前端框架开发',
              highlight: '实战项目',
              categoryId: 3, // 橙色极光
              onTap: () {},
            ),
            const SizedBox(height: 16),
            
            // 粉色极光系
            GradientCourseCard(
              courseName: 'UI/UX设计',
              description: '用户界面与体验设计',
              highlight: '设计思维',
              categoryId: 4, // 粉色极光
              onTap: () {},
            ),
            const SizedBox(height: 16),
            
            // 青色极光系
            GradientCourseCard(
              courseName: 'Flutter移动开发',
              description: '跨平台移动应用开发',
              highlight: '移动端',
              categoryId: 5, // 青色极光
              onTap: () {},
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
