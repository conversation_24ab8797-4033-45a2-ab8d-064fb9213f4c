import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../services/cache_service.dart';

// 将 JSON 解码放到后台 Isolate，减少主线程 CPU 峰值
dynamic _parseJson(String body) => jsonDecode(body);

class ApiCacheProvider with ChangeNotifier {
  final Map<String, DateTime> _cacheTimestamps = {};
  final Duration _cacheExpiration = const Duration(hours: 1); // 缓存过期时间

  // 带缓存的GET请求
  Future<dynamic> getWithCache(
    String url, {
    Map<String, String>? headers,
    bool useMemoryCache = true,
    bool usePersistentCache = false,
    bool forceRefresh = false,
  }) async {
    String cacheKey = 'api_cache_$url';

    // 如果不强制刷新且使用内存缓存，检查内存缓存中是否有数据
    if (!forceRefresh &&
        useMemoryCache &&
        CacheService.hasCachedData(cacheKey)) {
      // 检查缓存是否过期
      if (_cacheTimestamps.containsKey(cacheKey)) {
        DateTime timestamp = _cacheTimestamps[cacheKey]!;
        if (DateTime.now().difference(timestamp) < _cacheExpiration) {
          return CacheService.getCachedData(cacheKey);
        }
      }
    }

    // 如果不强制刷新且使用持久化缓存，检查持久化缓存中是否有数据
    if (!forceRefresh && usePersistentCache) {
      dynamic persistedData = await CacheService.getPersistedData(cacheKey);
      if (persistedData != null) {
        // 同时更新内存缓存
        if (useMemoryCache) {
          CacheService.cacheData(cacheKey, persistedData);
          _cacheTimestamps[cacheKey] = DateTime.now();
        }
        return persistedData;
      }
    }

    // 如果没有缓存或需要刷新，则发起网络请求
    try {
      final response = await http.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        // 使用后台 Isolate 进行 JSON 解码，降低主线程负载
        dynamic responseData = await compute(_parseJson, response.body);

        // 更新内存缓存
        if (useMemoryCache) {
          CacheService.cacheData(cacheKey, responseData);
          _cacheTimestamps[cacheKey] = DateTime.now();
        }

        // 更新持久化缓存
        if (usePersistentCache) {
          await CacheService.persistData(cacheKey, responseData);
        }

        return responseData;
      } else {
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      // 如果请求失败但有缓存，则返回缓存数据
      if (useMemoryCache && CacheService.hasCachedData(cacheKey)) {
        return CacheService.getCachedData(cacheKey);
      } else if (usePersistentCache) {
        dynamic persistedData = await CacheService.getPersistedData(cacheKey);
        if (persistedData != null) return persistedData;
      }

      // 如果连缓存都没有，则抛出异常
      rethrow;
    }
  }

  // 清除特定URL的缓存
  void clearCache(
    String url, {
    bool clearMemory = true,
    bool clearPersistent = false,
  }) async {
    String cacheKey = 'api_cache_$url';
    if (clearMemory) {
      CacheService.clearCache(cacheKey);
      _cacheTimestamps.remove(cacheKey);
    }
    if (clearPersistent) {
      await CacheService.clearPersistedCache(cacheKey);
    }
    notifyListeners();
  }

  // 清除所有缓存
  void clearAllCache({
    bool clearMemory = true,
    bool clearPersistent = false,
  }) async {
    if (clearMemory) {
      CacheService.clearAllCache();
      _cacheTimestamps.clear();
    }
    if (clearPersistent) {
      await CacheService.clearAllPersistedCache();
    }
    notifyListeners();
  }
}
