import 'package:flutter/material.dart';
import 'package:wakeup_app/core/utils/logger.dart';
import 'package:wakeup_app/core/services/storage_service.dart';

/// 用户状态 Provider
///
/// 说明：
/// - 统一改用 StorageService 管理本地存储，避免与其它模块（如 AuthService）
///   使用不同的 key（例如 token vs auth_token）导致登录态不一致。
/// - 提供基本的用户资料与登录态管理。
class UserProvider extends ChangeNotifier {
  int _userId = 0;
  String? _token;
  String? _avatarUrl;
  String? _nickname;
  bool _isLoading = true;
  bool _courseChanged = false;

  int get userId => _userId;
  String? get token => _token;
  String? get avatarUrl => _avatarUrl;
  String? get nickname => _nickname;
  bool get isLoading => _isLoading;
  bool get courseChanged => _courseChanged;

  UserProvider() {
    loadFromPrefs();
  }

  /// 设置用户基本信息（登录成功时调用）
  ///
  /// 参数：
  /// - id: 用户ID
  /// - token: 认证 Token
  /// - avatarUrl: 头像URL（可选）
  /// - nickname: 昵称（可选）
  void setUser(int id, String token, {String? avatarUrl, String? nickname}) {
    AppLogger.info(
      '设置用户: id=$id, token=$token, avatarUrl=$avatarUrl, nickname=$nickname',
    );
    _userId = id;
    _token = token;
    _avatarUrl = avatarUrl;
    _nickname = nickname;
    _saveToPrefs();
    notifyListeners();
  }

  /// 更新头像地址并持久化
  void setAvatarUrl(String url) {
    AppLogger.info('设置用户头像: $url');
    _avatarUrl = url;
    _saveToPrefs();
    notifyListeners();
  }

  /// 更新昵称并持久化
  void setNickname(String nickname) {
    AppLogger.info('设置用户昵称: $nickname');
    _nickname = nickname;
    _saveToPrefs();
    notifyListeners();
  }

  /// 从本地存储加载用户信息
  Future<void> loadFromPrefs() async {
    _isLoading = true;
    notifyListeners();

    try {
      // 统一使用 StorageService，确保 key 与 AuthService 一致
      _userId = await StorageService.instance.getInt('userId') ?? 0;
      _token = await StorageService.instance.getAuthToken();
      _avatarUrl = await StorageService.instance.getString('avatarUrl');
      _nickname = await StorageService.instance.getString('nickname');

      AppLogger.info(
        '从本地加载用户信息: userId=$_userId, token=$_token, avatarUrl=$_avatarUrl, nickname=$_nickname',
      );

      if (_userId != 0 && (_token?.isNotEmpty ?? false)) {
        AppLogger.info('本地存在登录状态');
      } else {
        AppLogger.info('本地无登录状态');
      }
    } catch (e) {
      AppLogger.error('加载用户信息错误', error: e);
      _userId = 0;
      _token = null;
      _avatarUrl = null;
      _nickname = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 清除用户信息（登出时调用）
  void clearUser() async {
    AppLogger.info('清除用户信息');
    _userId = 0;
    _token = null;
    _avatarUrl = null;
    _nickname = null;

    try {
      await StorageService.instance.remove('userId');
      await StorageService.instance.clearAuthToken();
      await StorageService.instance.remove('avatarUrl');
      await StorageService.instance.remove('nickname');
      AppLogger.info('已从本地存储中移除用户信息');
    } catch (e) {
      AppLogger.error('清除用户信息错误', error: e);
    }

    notifyListeners();
  }

  /// 持久化当前内存中的用户信息
  Future<void> _saveToPrefs() async {
    try {
      await StorageService.instance.setInt('userId', _userId);
      await StorageService.instance.setAuthToken(_token ?? '');
      if (_avatarUrl != null) {
        await StorageService.instance.setString('avatarUrl', _avatarUrl!);
      }
      if (_nickname != null) {
        await StorageService.instance.setString('nickname', _nickname!);
      }
      AppLogger.success('用户信息已保存到本地存储');
    } catch (e) {
      AppLogger.error('保存用户信息错误', error: e);
    }
  }

  /// 通知课程数据发生变化（用于触发学习页刷新）
  void notifyCourseChange() {
    _courseChanged = true;
    notifyListeners();
    AppLogger.info('已通知课程数据变化');
  }

  /// 重置课程变化标志
  void resetCourseChanged() {
    _courseChanged = false;
    AppLogger.info('重置课程数据变化标志');
  }

  /// 是否已登录（根据 userId 与 token 判定）
  bool get isLoggedIn => _userId != 0 && (_token?.isNotEmpty ?? false);
}
