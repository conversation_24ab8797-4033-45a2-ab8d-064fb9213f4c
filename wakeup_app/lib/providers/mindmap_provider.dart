import 'package:flutter/foundation.dart';
import '../models/knowledge_node_model.dart';
import '../services/mindmap_service.dart';
import '../core/utils/logger.dart';

/// 思维导图状态管理Provider
/// 
/// 管理思维导图的状态、数据和用户交互
class MindMapProvider extends ChangeNotifier {
  /// 思维导图服务实例
  final MindMapService _mindMapService = MindMapService();

  /// 根节点
  KnowledgeNode? _rootNode;

  /// 当前状态
  MindMapState _state = MindMapState.initial;

  /// 错误信息
  MindMapError? _error;

  /// 正在展开的节点ID集合
  final Set<String> _expandingNodes = {};

  /// 获取根节点
  KnowledgeNode? get rootNode => _rootNode;

  /// 获取当前状态
  MindMapState get state => _state;

  /// 获取错误信息
  MindMapError? get error => _error;

  /// 是否正在加载
  bool get isLoading => _state == MindMapState.loading;

  /// 是否有错误
  bool get hasError => _state == MindMapState.error;

  /// 检查指定节点是否正在展开
  /// 
  /// 参数：
  /// - nodeId: 节点ID
  /// 
  /// 返回：是否正在展开
  bool isNodeExpanding(String nodeId) => _expandingNodes.contains(nodeId);

  /// 获取所有展开的节点
  /// 
  /// 返回：展开的节点列表，按层级顺序排列
  List<KnowledgeNode> getExpandedNodes() {
    if (_rootNode == null) return [];
    return _rootNode!.getExpandedNodes();
  }

  /// 初始化思维导图
  /// 
  /// 创建根节点并加载初始数据
  /// 
  /// 参数：
  /// - topic: 根节点主题
  Future<void> initializeMindMap(String topic) async {
    try {
      AppLogger.info('初始化思维导图: $topic');
      
      _setState(MindMapState.loading);
      _clearError();

      // 创建根节点
      _rootNode = KnowledgeNode.createRoot(topic);
      notifyListeners();

      // 获取根节点的知识分支
      final rootData = await _mindMapService.getBranches(topic);
      
      // 更新根节点
      _rootNode = rootData.copyWith(
        id: _rootNode!.id, // 保持原有ID
        isExpanded: true,  // 根节点始终展开
      );

      _setState(MindMapState.loaded);
      AppLogger.success('思维导图初始化完成');
      
    } catch (e) {
      AppLogger.error('初始化思维导图失败', error: e);
      _setError(MindMapError(
        message: '初始化失败: ${e.toString()}',
        details: e,
      ));
    }
  }

  /// 展开知识节点
  /// 
  /// 获取节点的详细内容和子节点
  /// 
  /// 参数：
  /// - nodeId: 要展开的节点ID
  Future<void> expandNode(String nodeId) async {
    if (_rootNode == null) {
      AppLogger.warning('根节点不存在，无法展开节点');
      return;
    }

    // 检查节点是否正在展开
    if (_expandingNodes.contains(nodeId)) {
      AppLogger.info('节点正在展开中，跳过重复请求: $nodeId');
      return;
    }

    try {
      AppLogger.info('开始展开节点: $nodeId');
      
      // 查找要展开的节点
      final targetNode = _findNodeById(nodeId);
      if (targetNode == null) {
        AppLogger.warning('未找到要展开的节点: $nodeId');
        return;
      }

      // 如果节点已经展开，直接返回
      if (targetNode.isExpanded) {
        AppLogger.info('节点已经展开: $nodeId');
        return;
      }

      // 标记节点为正在展开
      _expandingNodes.add(nodeId);
      _setState(MindMapState.expanding);

      // 展开节点
      final expandedNode = await _mindMapService.expandNode(targetNode);
      
      // 更新节点树
      _rootNode = _rootNode!.updateNodeById(nodeId, expandedNode);
      
      // 移除展开标记
      _expandingNodes.remove(nodeId);
      _setState(MindMapState.loaded);
      
      AppLogger.success('节点展开完成: $nodeId');
      
    } catch (e) {
      AppLogger.error('展开节点失败', error: e);
      
      // 移除展开标记
      _expandingNodes.remove(nodeId);
      
      _setError(MindMapError(
        message: '展开节点失败: ${e.toString()}',
        details: e,
      ));
    }
  }

  /// 重新加载思维导图
  /// 
  /// 清除当前数据并重新初始化
  /// 
  /// 参数：
  /// - topic: 新的根节点主题（可选，默认使用当前主题）
  Future<void> reload([String? topic]) async {
    final currentTopic = topic ?? _rootNode?.title ?? '';
    if (currentTopic.isEmpty) {
      AppLogger.warning('无法重新加载：缺少主题');
      return;
    }

    AppLogger.info('重新加载思维导图: $currentTopic');
    
    // 清除当前状态
    _rootNode = null;
    _expandingNodes.clear();
    _clearError();
    
    // 重新初始化
    await initializeMindMap(currentTopic);
  }

  /// 清除错误状态
  void clearError() {
    _clearError();
    if (_state == MindMapState.error) {
      _setState(MindMapState.loaded);
    }
  }

  /// 根据ID查找节点
  /// 
  /// 参数：
  /// - nodeId: 节点ID
  /// 
  /// 返回：找到的节点，如果未找到则返回null
  KnowledgeNode? _findNodeById(String nodeId) {
    if (_rootNode == null) return null;
    
    if (_rootNode!.id == nodeId) {
      return _rootNode;
    }
    
    return _rootNode!.findChildById(nodeId);
  }

  /// 设置状态
  /// 
  /// 参数：
  /// - newState: 新状态
  void _setState(MindMapState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// 设置错误信息
  /// 
  /// 参数：
  /// - error: 错误信息
  void _setError(MindMapError error) {
    _error = error;
    _setState(MindMapState.error);
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
  }

  /// 获取节点统计信息
  /// 
  /// 返回：包含节点统计的Map
  Map<String, int> getStatistics() {
    if (_rootNode == null) {
      return {
        'totalNodes': 0,
        'expandedNodes': 0,
        'maxLevel': 0,
      };
    }

    final expandedNodes = getExpandedNodes();
    final maxLevel = expandedNodes.isEmpty 
        ? 0 
        : expandedNodes.map((node) => node.level).reduce((a, b) => a > b ? a : b);

    return {
      'totalNodes': expandedNodes.length,
      'expandedNodes': expandedNodes.where((node) => node.isExpanded).length,
      'maxLevel': maxLevel,
    };
  }

  @override
  void dispose() {
    _mindMapService.dispose();
    super.dispose();
    AppLogger.info('MindMapProvider 已释放');
  }
}
