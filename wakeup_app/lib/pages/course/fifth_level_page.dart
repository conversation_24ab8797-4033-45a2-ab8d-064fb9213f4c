import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../services/auth_service.dart';

class FifthLevelPage extends StatefulWidget {
  final int courseId;
  final String courseName;

  const FifthLevelPage({
    super.key,
    required this.courseId,
    required this.courseName,
  });

  @override
  State<FifthLevelPage> createState() => _FifthLevelPageState();
}

class _FifthLevelPageState extends State<FifthLevelPage> {
  late Future<List<dynamic>> futureQuestions;

  @override
  void initState() {
    super.initState();
    futureQuestions = fetchQuestions(widget.courseId);
  }

  Future<List<dynamic>> fetchQuestions(int courseId) async {
    final response = await http.get(
      Uri.parse("${AuthService.baseUrl}/api/questions?course_id=$courseId"),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception("加载题库失败");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.courseName)),
      body: FutureBuilder<List<dynamic>>(
        future: futureQuestions,
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            final questions = snapshot.data!;
            return ListView.builder(
              itemCount: questions.length,
              itemBuilder: (context, index) {
                final q = questions[index];
                return Card(
                  margin: const EdgeInsets.all(10),
                  child: ListTile(
                    title: Text(q["content"] ?? "题目"),
                    subtitle: Text("答案：${q["answer"]}"),
                    isThreeLine: true,
                  ),
                );
              },
            );
          } else if (snapshot.hasError) {
            return Center(child: Text("加载失败: ${snapshot.error}"));
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }
}
