import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/article_model.dart';
// import 'dart:ui'; // 移除：已由 Flutter 提供

class ArticleDetailPage extends StatefulWidget {
  final Article article;

  const ArticleDetailPage({super.key, required this.article});

  @override
  State<ArticleDetailPage> createState() => _ArticleDetailPageState();
}

class _ArticleDetailPageState extends State<ArticleDetailPage>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  double _scrollProgress = 0.0;
  bool _showAppBar = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    // 启动进入动画
    _fadeController.forward();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final offset = _scrollController.offset;
    final progress = (offset / 300).clamp(0.0, 1.0);

    setState(() {
      _scrollProgress = progress;
      _showAppBar = offset > 200;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: Stack(
        children: [
          // 背景图片 - 与资讯库页面保持一致
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/traditional_quiz_bg.png'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // 主要内容
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // 极简导航栏
              _buildMinimalNavigationBar(),

              // 文章内容
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildArticleContent(),
                ),
              ),

              // 底部间距
              const SliverToBoxAdapter(child: SizedBox(height: 120)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMinimalNavigationBar() {
    return SliverAppBar(
      expandedHeight: 0,
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.black.withValues(alpha: _scrollProgress * 0.9),
      leading: Container(
        margin: const EdgeInsets.only(left: 12),
        child: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            HapticFeedback.lightImpact();
            Navigator.of(context).pop();
          },
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(CupertinoIcons.back, color: Colors.white, size: 18),
          ),
        ),
      ),
      title: AnimatedOpacity(
        opacity: _showAppBar ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Text(
          widget.article.title,
          style: TextStyle(
            color: Colors.white,
            fontSize: 17,
            fontWeight: FontWeight.w500,
            letterSpacing: -0.2,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildArticleContent() {
    return Container(
      margin: const EdgeInsets.only(top: 80), // 为导航栏留出空间
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区域 - 极简设计
          _buildTitle(),

          // 极简元信息
          _buildSimpleMetaInfo(),

          // 正文内容 - 专注阅读体验
          _buildMainContent(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      margin: const EdgeInsets.fromLTRB(28, 0, 28, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主标题 - 极简大标题
          Text(
            widget.article.title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 34, // 大标题
              fontWeight: FontWeight.w700,
              height: 1.1, // 紧凑行高
              letterSpacing: -1.0, // Apple风格负字间距
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleMetaInfo() {
    return Container(
      margin: const EdgeInsets.fromLTRB(28, 0, 28, 40),
      child: Row(
        children: [
          // 简化的作者信息
          if (widget.article.authorName.isNotEmpty) ...[
            Text(
              widget.article.authorName,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 14,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.1,
              ),
            ),
            Text(
              ' · ',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.4),
                fontSize: 14,
              ),
            ),
          ],

          // 发布日期
          Text(
            widget.article.formattedDate,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 14,
              fontWeight: FontWeight.w400,
              letterSpacing: 0.1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      margin: const EdgeInsets.fromLTRB(28, 0, 28, 0),
      child: Text(
        widget.article.content,
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.95),
          fontSize: 19, // 舒适的阅读字号
          fontWeight: FontWeight.w400,
          height: 1.8, // 舒适的行高
          letterSpacing: -0.2, // 微调字间距
        ),
      ),
    );
  }
}
