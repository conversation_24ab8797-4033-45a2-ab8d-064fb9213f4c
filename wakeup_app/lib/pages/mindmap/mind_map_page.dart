import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import '../../models/knowledge_node_model.dart';
import '../../providers/mindmap_provider.dart';
import '../../widgets/knowledge_card.dart';
import '../../widgets/connection_line.dart';
import '../../core/utils/logger.dart';

/// 思维导图主页面
///
/// 提供知识探索的交互式思维导图界面
class MindMapPage extends StatefulWidget {
  final ValueChanged<bool>? onFocusModeChanged;

  const MindMapPage({super.key, this.onFocusModeChanged});

  @override
  State<MindMapPage> createState() => _MindMapPageState();
}

class _MindMapPageState extends State<MindMapPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _topicController = TextEditingController();
  bool _focusMode = false;

  @override
  void initState() {
    super.initState();

    // 初始化默认主题
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeWithDefaultTopic();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _topicController.dispose();
    super.dispose();
  }

  /// 使用默认主题初始化思维导图
  void _initializeWithDefaultTopic() {
    final provider = Provider.of<MindMapProvider>(context, listen: false);
    provider.initializeMindMap('机器学习');
    _topicController.text = '机器学习';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MindMapProvider>(
      builder: (context, provider, child) {
        return CupertinoPageScaffold(
          navigationBar: _buildNavigationBar(provider),
          child: SafeArea(
            child: Column(
              children: [
                if (!_focusMode) _buildTopicInput(provider),
                Expanded(child: _buildMindMapContent(provider)),
                if (!_focusMode) _buildBottomControls(provider),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建导航栏
  CupertinoNavigationBar _buildNavigationBar(MindMapProvider provider) {
    return CupertinoNavigationBar(
      middle: const Text('思绪星图'),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 专注模式切换
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: _toggleFocusMode,
            child: Icon(
              _focusMode ? CupertinoIcons.eye_slash : CupertinoIcons.eye,
              size: 20,
            ),
          ),
          // 刷新按钮
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed:
                provider.isLoading ? null : () => _refreshMindMap(provider),
            child:
                provider.isLoading
                    ? const CupertinoActivityIndicator(radius: 8)
                    : const Icon(CupertinoIcons.refresh, size: 20),
          ),
        ],
      ),
    );
  }

  /// 构建主题输入区域
  Widget _buildTopicInput(MindMapProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        border: Border(
          bottom: BorderSide(color: CupertinoColors.separator, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: CupertinoTextField(
              controller: _topicController,
              placeholder: '输入要探索的主题...',
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              onSubmitted: (value) => _startExploration(provider, value),
            ),
          ),
          const SizedBox(width: 12),
          CupertinoButton(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: CupertinoColors.activeBlue,
            borderRadius: BorderRadius.circular(8),
            onPressed:
                provider.isLoading
                    ? null
                    : () => _startExploration(provider, _topicController.text),
            child:
                provider.isLoading
                    ? const CupertinoActivityIndicator(
                      color: CupertinoColors.white,
                    )
                    : const Text(
                      '探索',
                      style: TextStyle(color: CupertinoColors.white),
                    ),
          ),
        ],
      ),
    );
  }

  /// 构建思维导图内容区域
  Widget _buildMindMapContent(MindMapProvider provider) {
    if (provider.hasError) {
      return _buildErrorView(provider);
    }

    if (provider.isLoading && provider.rootNode == null) {
      return _buildLoadingView();
    }

    if (provider.rootNode == null) {
      return _buildEmptyView();
    }

    return _buildMindMapTree(provider);
  }

  /// 构建底部控制区域
  Widget _buildBottomControls(MindMapProvider provider) {
    final stats = provider.getStatistics();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        border: Border(
          top: BorderSide(color: CupertinoColors.separator, width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('节点', stats['totalNodes']!),
          _buildStatItem('已展开', stats['expandedNodes']!),
          _buildStatItem('层级', stats['maxLevel']!),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, int value) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value.toString(),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: CupertinoColors.activeBlue,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: CupertinoColors.secondaryLabel,
          ),
        ),
      ],
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(MindMapProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.exclamationmark_triangle,
              size: 64,
              color: CupertinoColors.systemRed,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: CupertinoColors.label,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              provider.error?.message ?? '未知错误',
              style: TextStyle(
                fontSize: 14,
                color: CupertinoColors.secondaryLabel,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CupertinoButton.filled(
              onPressed: () => _refreshMindMap(provider),
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CupertinoActivityIndicator(radius: 16),
          SizedBox(height: 16),
          Text(
            '正在探索知识宇宙...',
            style: TextStyle(
              fontSize: 16,
              color: CupertinoColors.secondaryLabel,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.lightbulb,
              size: 64,
              color: CupertinoColors.systemYellow,
            ),
            const SizedBox(height: 16),
            Text(
              '开始知识探索',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: CupertinoColors.label,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '输入一个主题，开始你的学习之旅',
              style: TextStyle(
                fontSize: 14,
                color: CupertinoColors.secondaryLabel,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建思维导图树
  Widget _buildMindMapTree(MindMapProvider provider) {
    final expandedNodes = provider.getExpandedNodes();

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final node = expandedNodes[index];
              return _buildNodeWithConnections(
                node,
                provider,
                index,
                expandedNodes,
              );
            }, childCount: expandedNodes.length),
          ),
        ),
      ],
    );
  }

  /// 构建带连接线的节点
  Widget _buildNodeWithConnections(
    KnowledgeNode node,
    MindMapProvider provider,
    int nodeIndex,
    List<KnowledgeNode> allNodes,
  ) {
    return Column(
      children: [
        // 连接线（如果不是根节点）
        if (node.level > 0) _buildConnectionToParent(node, allNodes),

        // 节点卡片
        KnowledgeCard(
          node: node,
          onTap: () => _expandNode(provider, node),
          isExpanding: provider.isNodeExpanding(node.id),
          width: MediaQuery.of(context).size.width - 32,
        ),

        // 子节点连接线
        if (node.isExpanded && node.hasChildren)
          _buildChildrenConnections(node, allNodes),
      ],
    );
  }

  /// 构建到父节点的连接线
  Widget _buildConnectionToParent(
    KnowledgeNode node,
    List<KnowledgeNode> allNodes,
  ) {
    // 查找父节点
    final parentNode = allNodes.firstWhere(
      (n) => n.id == node.parentId,
      orElse: () => node, // 如果找不到父节点，返回自己（不应该发生）
    );

    if (parentNode.id == node.id) return const SizedBox.shrink();

    // 计算子节点在父节点中的索引
    final childIndex = parentNode.children.indexWhere(
      (child) => child.id == node.id,
    );

    return ConnectionLine(
      startY: 0,
      endY: 100,
      parentLevel: parentNode.level,
      childIndex: childIndex,
      totalChildren: parentNode.children.length,
    );
  }

  /// 构建子节点连接线
  Widget _buildChildrenConnections(
    KnowledgeNode node,
    List<KnowledgeNode> allNodes,
  ) {
    return Column(
      children:
          node.children.asMap().entries.map((entry) {
            final index = entry.key;

            return ConnectionLine(
              startY: 0,
              endY: 80,
              parentLevel: node.level,
              childIndex: index,
              totalChildren: node.children.length,
            );
          }).toList(),
    );
  }

  /// 切换专注模式
  void _toggleFocusMode() {
    setState(() {
      _focusMode = !_focusMode;
    });

    // 通知父组件专注模式状态变化
    widget.onFocusModeChanged?.call(_focusMode);

    AppLogger.info('专注模式: ${_focusMode ? "开启" : "关闭"}');
  }

  /// 刷新思维导图
  void _refreshMindMap(MindMapProvider provider) {
    AppLogger.info('刷新思维导图');
    provider.reload();
  }

  /// 开始探索新主题
  void _startExploration(MindMapProvider provider, String topic) {
    if (topic.trim().isEmpty) {
      _showMessage('请输入要探索的主题');
      return;
    }

    AppLogger.info('开始探索主题: $topic');
    provider.initializeMindMap(topic.trim());
  }

  /// 展开节点
  void _expandNode(MindMapProvider provider, KnowledgeNode node) {
    if (node.isExpanded) {
      AppLogger.info('节点已展开: ${node.title}');
      return;
    }

    AppLogger.info('展开节点: ${node.title}');
    provider.expandNode(node.id);
  }

  /// 显示消息
  void _showMessage(String message) {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                child: const Text('确定'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
    );
  }
}
