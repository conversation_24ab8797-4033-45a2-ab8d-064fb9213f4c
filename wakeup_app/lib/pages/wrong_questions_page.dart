import 'dart:io' show Platform;
import 'dart:ui';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/fonts.dart';
import '../widgets/back_button.dart';
import 'quiz/quiz_attempt_page.dart' as quiz_page;
import '../core/utils/logger.dart';

class WrongQuestionsPage extends StatefulWidget {
  final int? courseId;

  const WrongQuestionsPage({super.key, this.courseId});

  @override
  State<WrongQuestionsPage> createState() => _WrongQuestionsPageState();
}

class _WrongQuestionsPageState extends State<WrongQuestionsPage>
    with TickerProviderStateMixin {
  List<Map<String, dynamic>> _wrongQuestions = [];
  String _selectedTab = "current";
  TabController? _tabController;

  bool _isCurrentCourseLoading = true;
  List<Map<String, dynamic>> _currentCourseWrongQuestions = [];
  bool _isAllCoursesLoading = true;
  List<Map<String, dynamic>> _allCoursesWrongQuestions = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController!.addListener(() {
      final newIndex = _tabController!.index;
      if (newIndex == 0 && _selectedTab != "current") {
        setState(() {
          _selectedTab = "current";
        });
      } else if (newIndex == 1 && _selectedTab != "all") {
        setState(() {
          _selectedTab = "all";
        });
      }
    });
    _loadAndPrepareAllData();
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _loadAndPrepareAllData() async {
    setState(() {
      _isCurrentCourseLoading = true;
      _isAllCoursesLoading = true;
    });

    try {
      // 从SharedPreferences获取错题数据
      final prefs = await SharedPreferences.getInstance();
      final wrongQuestionsJson = prefs.getString('wrong_questions') ?? '[]';
      AppLogger.info('获取到错题列表JSON: $wrongQuestionsJson');

      final List decodedData = jsonDecode(wrongQuestionsJson);
      AppLogger.info('错题列表中有${decodedData.length}个项目');

      // 将JSON数据转换为可用的Map列表
      _wrongQuestions =
          decodedData
              .map<Map<String, dynamic>>(
                (item) => item is Map ? Map<String, dynamic>.from(item) : {},
              )
              .toList();

      // 移除可能出现的空Map
      _wrongQuestions.removeWhere((item) => item.isEmpty);

      AppLogger.info('处理后的错题列表包含${_wrongQuestions.length}个项目');
    } catch (e) {
      AppLogger.error('加载错题数据失败', error: e);
      // 出错时使用空列表
      _wrongQuestions = [];
    }

    _prepareTabData();
    if (mounted) {
      setState(() {});
    }
  }

  void _prepareTabData() {
    if (widget.courseId != null) {
      _currentCourseWrongQuestions =
          _wrongQuestions
              .where((q) => q['courseId'] == widget.courseId)
              .toList();
    } else {
      _currentCourseWrongQuestions = [];
    }
    _isCurrentCourseLoading = false;

    _allCoursesWrongQuestions = List.from(_wrongQuestions);
    _isAllCoursesLoading = false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/traditional_quiz_bg.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPageForTab("current"),
                    _buildPageForTab("all"),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          BackButtonWidget(
            text: "",
            color: Colors.white,
            onPressed: () => Navigator.of(context).pop(),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              "错题库",
              style:
                  Platform.isIOS &&
                          Localizations.localeOf(context).languageCode != 'zh'
                      ? TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        letterSpacing: -0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.7),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      )
                      : AppFonts.createTitleStyle(
                        fontSize: 18,
                        isLatinText:
                            Localizations.localeOf(context).languageCode !=
                            'zh',
                      ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          // 平衡布局的空白区域，保持与返回按钮宽度大致对称
          SizedBox(width: 34 + 16), // 34 for button, 16 for SizedBox
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      tabs: [Tab(text: "当前课程"), Tab(text: "全部课程")],
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
      indicatorColor: Colors.white,
      indicatorWeight: 3.0,
      indicatorSize: TabBarIndicatorSize.label,
      labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: 15,
      ),
    );
  }

  Widget _buildPageForTab(String tabType) {
    bool isLoading;
    List<Map<String, dynamic>> specificData;

    if (tabType == "current") {
      isLoading = _isCurrentCourseLoading;
      specificData = _currentCourseWrongQuestions;
    } else {
      // "all"
      isLoading = _isAllCoursesLoading;
      specificData = _allCoursesWrongQuestions;
    }

    if (isLoading) {
      return Center(child: CupertinoActivityIndicator());
    }
    if (specificData.isEmpty) {
      return _buildEmptyState();
    }
    return _buildWrongQuestionsList(specificData, tabType);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            CupertinoIcons.xmark_circle,
            size: 60,
            color: Colors.white.withValues(alpha: 0.6),
          ),
          SizedBox(height: 16),
          Text(
            _selectedTab == "current" && widget.courseId == null
                ? "未指定当前课程"
                : "暂无错题记录",
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            "回答错误的题目将自动添加到错题本",
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWrongQuestionsList(
    List<Map<String, dynamic>> currentWrongData,
    String tabType,
  ) {
    // 提取当前列表中所有题目的ID
    final List<int> questionIds =
        currentWrongData
            .map<int>((item) {
              // 确保正确处理id字段，可能是int或string
              var id = item['id'];
              if (id is int) {
                return id;
              } else if (id is String) {
                return int.parse(id);
              }
              // 如果无法解析，返回默认值-1
              AppLogger.warning('无法解析题目ID: $id');
              return -1;
            })
            .where((id) => id != -1) // 过滤掉无效的ID
            .toList();

    AppLogger.info(
      '${tabType == "current" ? "当前课程" : "全部课程"}题目ID列表: $questionIds',
    );

    // 构建题目ID到课程信息的映射
    final Map<int, Map<String, dynamic>> courseInfoMap = {};
    for (var item in currentWrongData) {
      try {
        final id =
            item['id'] is int ? item['id'] : int.parse(item['id'].toString());
        courseInfoMap[id] = {
          'courseId':
              item['courseId'] is int
                  ? item['courseId']
                  : int.parse(item['courseId'].toString()),
          'courseName': item['courseName'],
        };
      } catch (e) {
        AppLogger.error('构建课程信息映射出错', error: e);
      }
    }

    AppLogger.info('构建了包含${courseInfoMap.length}条记录的课程信息映射');

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: currentWrongData.length,
      itemBuilder: (context, index) {
        final item = currentWrongData[index];
        return _buildWrongQuestionCard(
          item,
          questionIds,
          tabType,
          courseInfoMap,
        );
      },
    );
  }

  Widget _buildWrongQuestionCard(
    Map<String, dynamic> item,
    List<int> allQuestionIds,
    String tabType,
    Map<int, Map<String, dynamic>> courseInfoMap,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        item['courseName'],
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        "错误${item['wrongCount']}次",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.red[200],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Spacer(),
                    Text(
                      item['date'],
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                Text(
                  item['content'],
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  children: [
                    _buildActionButton(
                      icon: CupertinoIcons.arrow_right_circle,
                      text: "重新答题",
                      onTap: () {
                        // 导航到答题页面，传递当前标签下的所有题目ID和课程信息映射
                        AppLogger.info(
                          '从${tabType == "current" ? "当前课程" : "全部课程"}错题库跳转到答题页面',
                        );
                        Navigator.of(context).push(
                          CupertinoPageRoute(
                            builder:
                                (context) => quiz_page.QuizAttemptPage(
                                  courseId: item['courseId'],
                                  courseName: item['courseName'],
                                  questionIds: allQuestionIds,
                                  courseInfoMap: courseInfoMap,
                                ),
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 12),
                    _buildActionButton(
                      icon: CupertinoIcons.check_mark_circled,
                      text: "标记已掌握",
                      onTap: () {
                        _showMarkAsMasteredConfirmation(item);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(icon, size: 16, color: Colors.white),
            SizedBox(width: 6),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示标记已掌握确认对话框
  Future<void> _showMarkAsMasteredConfirmation(
    Map<String, dynamic> item,
  ) async {
    bool? confirmed = await showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final bottomPadding = MediaQuery.of(context).padding.bottom;

        // 辅助方法构建操作项
        Widget buildActionItem(
          String text,
          VoidCallback onPressed, {
          bool isDestructive = false,
          FontWeight fontWeight = FontWeight.normal,
          Color? textColor,
        }) {
          return GestureDetector(
            onTap: onPressed,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              alignment: Alignment.center,
              child: Text(
                text,
                style: TextStyle(
                  color: textColor ?? Colors.green, // 默认使用绿色，标记已掌握使用绿色而不是红色
                  fontSize: 17.0, // 统一字体大小
                  fontWeight: fontWeight,
                ),
              ),
            ),
          );
        }

        return Padding(
          // 外层Padding，用于屏幕边缘和底部安全区域
          padding: EdgeInsets.only(
            left: 8.0,
            right: 8.0,
            bottom: bottomPadding > 0 ? bottomPadding : 8.0,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end, // 整体底部对齐
            children: [
              // ----- 标题和主要操作区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 15.0, sigmaY: 15.0), // 模糊效果
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withValues(alpha: 0.92), // 背景色
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 16.0,
                            horizontal: 20.0,
                          ),
                          child: Text(
                            "确定要将此题目标记为已掌握吗？",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Color(0xFFE5E5E7),
                              fontSize: 13.0,
                              fontWeight: FontWeight.bold, // 标题加粗
                            ),
                          ),
                        ),
                        // 分隔线
                        Divider(
                          height: 0.5,
                          thickness: 0.5,
                          color: (Colors.grey[700] ?? Colors.grey).withValues(
                            alpha: 0.5,
                          ),
                        ),
                        // "标记已掌握" 按钮
                        buildActionItem(
                          "标记已掌握",
                          () => Navigator.of(context).pop(true),
                          fontWeight: FontWeight.bold, // 按钮加粗
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8.0), // 主要操作区和取消按钮之间的间距
              // ----- 取消按钮区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 15.0, sigmaY: 15.0), // 模糊效果
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withValues(alpha: 0.92), // 背景色
                    ),
                    child: buildActionItem(
                      "取消",
                      () => Navigator.of(context).pop(false),
                      fontWeight: FontWeight.bold, // "取消"按钮加粗
                      textColor: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    if (confirmed == true) {
      await _removeFromWrongQuestions(item);
    }
  }

  // 从错题库中移除项目
  Future<void> _removeFromWrongQuestions(Map<String, dynamic> item) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wrongQuestionsJson = prefs.getString('wrong_questions') ?? '[]';
      AppLogger.info('获取到原错题列表: $wrongQuestionsJson');

      final List wrongQuestions = jsonDecode(wrongQuestionsJson);
      AppLogger.info('原错题列表包含${wrongQuestions.length}个项目');

      // 移除匹配的错题项
      final updatedWrongQuestions =
          wrongQuestions.where((wrongItem) {
            final bool shouldKeep =
                !(wrongItem is Map &&
                    wrongItem['id'] == item['id'] &&
                    wrongItem['courseId'] == item['courseId']);

            if (!shouldKeep) {
              AppLogger.info('找到要移除的错题项: ${wrongItem.toString()}');
            }
            return shouldKeep;
          }).toList();

      AppLogger.info('更新后错题列表包含${updatedWrongQuestions.length}个项目');

      // 保存更新后的错题列表
      final newWrongQuestionsJson = jsonEncode(updatedWrongQuestions);
      AppLogger.info('新错题列表JSON: $newWrongQuestionsJson');
      await prefs.setString('wrong_questions', newWrongQuestionsJson);
      AppLogger.success('已保存新错题列表到SharedPreferences');

      // 重新加载数据
      _loadAndPrepareAllData();
    } catch (e) {
      AppLogger.error('移除错题失败', error: e);
      if (mounted) {
        _showNotification('从错题库删除失败，请重试');
      }
    }
  }

  // 显示通知提示
  void _showNotification(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.red.shade800,
        duration: Duration(seconds: 2),
      ),
    );
  }
}
