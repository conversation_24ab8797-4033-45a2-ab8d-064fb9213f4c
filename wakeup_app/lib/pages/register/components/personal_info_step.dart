import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../constants/fonts.dart';

/// 个人信息步骤组件
class PersonalInfoStep extends StatefulWidget {
  final Map<String, dynamic> userData;
  final Function(String, dynamic) onDataChanged;

  const PersonalInfoStep({
    super.key,
    required this.userData,
    required this.onDataChanged,
  });

  @override
  State<PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends State<PersonalInfoStep> {
  late TextEditingController _usernameController;
  late TextEditingController _passwordController;
  late TextEditingController _confirmPasswordController;
  late TextEditingController _nicknameController;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController(text: widget.userData['username']);
    _passwordController = TextEditingController(text: widget.userData['password']);
    _confirmPasswordController = TextEditingController(text: widget.userData['confirmPassword']);
    _nicknameController = TextEditingController(text: widget.userData['nickname']);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nicknameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明文字 - 深色主题适配
          Text(
            '设置您的个人信息',
            style: AppFonts.createMixedStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
          
          const SizedBox(height: 24),
          
          _buildInputField(
            controller: _usernameController,
            placeholder: '请输入用户名',
            icon: CupertinoIcons.person,
            onChanged: (value) => widget.onDataChanged('username', value),
          ),
          
          const SizedBox(height: 16),
          
          _buildInputField(
            controller: _passwordController,
            placeholder: '请输入密码',
            icon: CupertinoIcons.lock,
            obscureText: true,
            onChanged: (value) => widget.onDataChanged('password', value),
          ),
          
          const SizedBox(height: 16),
          
          _buildInputField(
            controller: _confirmPasswordController,
            placeholder: '请确认密码',
            icon: CupertinoIcons.lock_fill,
            obscureText: true,
            onChanged: (value) => widget.onDataChanged('confirmPassword', value),
          ),
          
          const SizedBox(height: 16),
          
          _buildInputField(
            controller: _nicknameController,
            placeholder: '请输入昵称',
            icon: CupertinoIcons.smiley,
            onChanged: (value) => widget.onDataChanged('nickname', value),
          ),
          
          const SizedBox(height: 24),
          
          _buildGenderSelector(),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String placeholder,
    required IconData icon,
    bool obscureText = false,
    required Function(String) onChanged,
  }) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: CupertinoTextField(
        controller: controller,
        placeholder: placeholder,
        obscureText: obscureText,
        decoration: const BoxDecoration(),
        style: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        placeholderStyle: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.5),
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        onTap: () => HapticFeedback.selectionClick(),
        prefix: Container(
          padding: const EdgeInsets.only(left: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildGenderSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '性别',
          style: AppFonts.createMixedStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Container(
          height: 56,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.selectionClick();
                    widget.onDataChanged('gender', 'male');
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: widget.userData['gender'] == 'male'
                          ? Colors.white.withValues(alpha: 0.15)
                          : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(7),
                        bottomLeft: Radius.circular(7),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.person,
                          color: widget.userData['gender'] == 'male'
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.6),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '男',
                          style: AppFonts.createMixedStyle(
                            fontSize: 16,
                            fontWeight: widget.userData['gender'] == 'male'
                                ? FontWeight.w600
                                : FontWeight.w400,
                            color: widget.userData['gender'] == 'male'
                                ? Colors.white
                                : Colors.white.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                width: 1,
                height: 50,
                color: Colors.white.withValues(alpha: 0.2),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    HapticFeedback.selectionClick();
                    widget.onDataChanged('gender', 'female');
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: widget.userData['gender'] == 'female'
                          ? Colors.white.withValues(alpha: 0.15)
                          : Colors.transparent,
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(7),
                        bottomRight: Radius.circular(7),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          CupertinoIcons.person,
                          color: widget.userData['gender'] == 'female'
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.6),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '女',
                          style: AppFonts.createMixedStyle(
                            fontSize: 16,
                            fontWeight: widget.userData['gender'] == 'female'
                                ? FontWeight.w600
                                : FontWeight.w400,
                            color: widget.userData['gender'] == 'female'
                                ? Colors.white
                                : Colors.white.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}