import 'package:flutter/foundation.dart';
import '../../../core/services/api_client.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/utils/app_exceptions.dart';

/// 注册流程控制器
/// 从原有的3248行register_page中提取状态管理逻辑
class RegisterController extends ChangeNotifier {
  // 注册步骤枚举
  RegisterStep _currentStep = RegisterStep.phone;
  bool _isLoading = false;
  String? _errorMessage;

  // 用户输入数据
  final Map<String, dynamic> _userData = {};

  // Getters
  RegisterStep get currentStep => _currentStep;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic> get userData => Map.unmodifiable(_userData);

  // 表单验证状态
  bool get canProceedToNext {
    switch (_currentStep) {
      case RegisterStep.phone:
        return _userData['phone']?.isNotEmpty == true && 
               _isValidPhone(_userData['phone']);
      case RegisterStep.phoneVerification:
        return _userData['phoneCode']?.isNotEmpty == true &&
               _userData['phoneCode']?.length == 6;
      case RegisterStep.email:
        return _userData['email']?.isNotEmpty == true &&
               _isValidEmail(_userData['email']);
      case RegisterStep.emailVerification:
        return _userData['emailCode']?.isNotEmpty == true &&
               _userData['emailCode']?.length == 6;
      case RegisterStep.username:
        return _userData['username']?.isNotEmpty == true &&
               _userData['username']?.length >= 3;
      case RegisterStep.password:
        return _userData['password']?.isNotEmpty == true &&
               _userData['password']?.length >= 6;
      case RegisterStep.confirmPassword:
        return _userData['confirmPassword'] == _userData['password'];
      case RegisterStep.nickname:
        return _userData['nickname']?.isNotEmpty == true;
      case RegisterStep.gender:
        return _userData['gender'] != null;
      case RegisterStep.region:
        return _userData['region'] != null;
      case RegisterStep.courses:
        return _userData['selectedCourses']?.isNotEmpty == true;
    }
  }

  /// 设置用户数据
  void setUserData(String key, dynamic value) {
    _userData[key] = value;
    _clearError();
    notifyListeners();
  }

  /// 下一步
  Future<void> nextStep() async {
    if (!canProceedToNext) return;

    // 特殊步骤需要验证
    switch (_currentStep) {
      case RegisterStep.phone:
        await _sendPhoneVerification();
        break;
      case RegisterStep.phoneVerification:
        await _verifyPhoneCode();
        break;
      case RegisterStep.email:
        await _sendEmailVerification();
        break;
      case RegisterStep.emailVerification:
        await _verifyEmailCode();
        break;
      case RegisterStep.courses:
        await _completeRegistration();
        return; // 注册完成，不继续下一步
      default:
        _goToNextStep();
        break;
    }
  }

  /// 上一步
  void previousStep() {
    if (_currentStep.index > 0) {
      _currentStep = RegisterStep.values[_currentStep.index - 1];
      _clearError();
      notifyListeners();
    }
  }

  /// 跳转到下一步
  void _goToNextStep() {
    if (_currentStep.index < RegisterStep.values.length - 1) {
      _currentStep = RegisterStep.values[_currentStep.index + 1];
      _clearError();
      notifyListeners();
    }
  }

  /// 发送手机验证码
  Future<void> _sendPhoneVerification() async {
    _setLoading(true);
    try {
      final client = ApiClient();
      final response = await client.post<Map<String, dynamic>>(
        '/api/auth/send-phone-code',
        body: {'phone': _userData['phone']},
        parser: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess) {
        _goToNextStep();
      } else {
        throw ApiException(response.error?.message ?? '发送验证码失败');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 验证手机验证码
  Future<void> _verifyPhoneCode() async {
    _setLoading(true);
    try {
      final client = ApiClient();
      final response = await client.post<Map<String, dynamic>>(
        '/api/auth/verify-phone-code',
        body: {
          'phone': _userData['phone'],
          'code': _userData['phoneCode'],
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess) {
        _goToNextStep();
      } else {
        throw ApiException('验证码错误');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 发送邮箱验证码
  Future<void> _sendEmailVerification() async {
    _setLoading(true);
    try {
      final client = ApiClient();
      final response = await client.post<Map<String, dynamic>>(
        '/api/auth/send-email-code',
        body: {'email': _userData['email']},
        parser: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess) {
        _goToNextStep();
      } else {
        throw ApiException('发送验证码失败');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 验证邮箱验证码
  Future<void> _verifyEmailCode() async {
    _setLoading(true);
    try {
      final client = ApiClient();
      final response = await client.post<Map<String, dynamic>>(
        '/api/auth/verify-email-code',
        body: {
          'email': _userData['email'],
          'code': _userData['emailCode'],
        },
        parser: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess) {
        _goToNextStep();
      } else {
        throw ApiException('验证码错误');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 完成注册
  Future<void> _completeRegistration() async {
    _setLoading(true);
    try {
      final client = ApiClient();
      final response = await client.post<Map<String, dynamic>>(
        '/api/auth/register',
        body: _userData,
        parser: (data) => data as Map<String, dynamic>,
      );

      if (response.isSuccess) {
        final data = response.data!;
        // 保存认证信息
        if (data.containsKey('token')) {
          await StorageService.instance.setAuthToken(data['token']);
        }
        if (data.containsKey('user')) {
          await StorageService.instance.setUserInfo(data['user']);
        }
        
        // 注册成功
        notifyListeners();
      } else {
        throw ApiException('注册失败');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 验证手机号格式
  bool _isValidPhone(String phone) {
    return RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
  }

  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// 重置注册流程
  void reset() {
    _currentStep = RegisterStep.phone;
    _userData.clear();
    _clearError();
    _setLoading(false);
  }

  /// 获取当前步骤标题
  String getCurrentStepTitle() {
    switch (_currentStep) {
      case RegisterStep.phone:
        return '手机号验证';
      case RegisterStep.phoneVerification:
        return '验证手机号';
      case RegisterStep.email:
        return '邮箱验证';
      case RegisterStep.emailVerification:
        return '验证邮箱';
      case RegisterStep.username:
        return '设置用户名';
      case RegisterStep.password:
        return '设置密码';
      case RegisterStep.confirmPassword:
        return '确认密码';
      case RegisterStep.nickname:
        return '设置昵称';
      case RegisterStep.gender:
        return '选择性别';
      case RegisterStep.region:
        return '选择地区';
      case RegisterStep.courses:
        return '选择课程';
    }
  }

  /// 获取进度百分比
  double get progress => (_currentStep.index + 1) / RegisterStep.values.length;
}

/// 注册步骤枚举
enum RegisterStep {
  phone,              // 手机号输入
  phoneVerification,  // 手机号验证码验证
  email,              // 邮箱输入
  emailVerification,  // 邮箱验证码验证
  username,           // 用户名
  password,           // 密码
  confirmPassword,    // 确认密码
  nickname,           // 昵称
  gender,             // 性别
  region,             // 地区
  courses,            // 选课
}