import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../constants/fonts.dart';
import 'controllers/register_controller.dart';
import 'components/register_step_indicator.dart';
import 'components/phone_verification_step.dart';
import 'components/email_verification_step.dart';
import 'components/personal_info_step.dart';
import 'components/course_selection_step.dart';

/// 重构后的注册页面
/// 从原3248行的巨大文件分离出来，采用组件化架构
class RefactoredRegisterPage extends StatelessWidget {
  const RefactoredRegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => RegisterController(),
      child: const _RegisterPageContent(),
    );
  }
}

class _RegisterPageContent extends StatelessWidget {
  const _RegisterPageContent();

  @override
  Widget build(BuildContext context) {
    return Consumer<RegisterController>(
      builder: (context, controller, child) {
        return CupertinoPageScaffold(
          backgroundColor: Colors.black,
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
              HapticFeedback.selectionClick();
            },
            child: Stack(
              children: [
                // 深色沉浸式背景 - 与登录页面相同
                _buildDarkBackground(),
                
                // 主要内容
                SafeArea(
                  child: Column(
                    children: [
                      // 自定义导航栏
                      _buildCustomNavigationBar(context, controller),
                      
                      // 简化的步骤指示器 - 只显示当前步骤标题
                      RegisterStepIndicator(
                        currentStep: controller.currentStep,
                      ),
                      
                      // 错误信息
                      if (controller.errorMessage != null)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.08),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.4),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                CupertinoIcons.exclamationmark_triangle,
                                color: Colors.red.withValues(alpha: 0.8),
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  controller.errorMessage!,
                                  style: AppFonts.createMixedStyle(
                                    fontSize: 14,
                                    color: Colors.red.withValues(alpha: 0.8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      
                      // 当前步骤内容
                      Expanded(
                        child: SingleChildScrollView(
                          physics: const BouncingScrollPhysics(),
                          child: _buildCurrentStep(context, controller),
                        ),
                      ),
                      
                      // 底部按钮
                      _buildBottomButtons(context, controller),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCurrentStep(BuildContext context, RegisterController controller) {
    switch (controller.currentStep) {
      case RegisterStep.phone:
        return PhoneVerificationStep(
          initialPhone: controller.userData['phone'],
          onPhoneChanged: (phone) => controller.setUserData('phone', phone),
          onSendCode: controller.canProceedToNext ? () => controller.nextStep() : null,
          isLoading: controller.isLoading,
        );
        
      case RegisterStep.phoneVerification:
        return PhoneVerificationStep(
          initialPhone: controller.userData['phone'],
          onPhoneChanged: (phone) => controller.setUserData('phone', phone),
          onSendCode: () => controller.setUserData('phoneCode', ''), // 重新发送
          isLoading: controller.isLoading,
        );
        
      case RegisterStep.email:
        return EmailVerificationStep(
          initialEmail: controller.userData['email'],
          onEmailChanged: (email) => controller.setUserData('email', email),
          onSendCode: controller.canProceedToNext ? () => controller.nextStep() : null,
          isLoading: controller.isLoading,
        );
        
      case RegisterStep.emailVerification:
        return EmailVerificationStep(
          initialEmail: controller.userData['email'],
          onEmailChanged: (email) => controller.setUserData('email', email),
          onCodeChanged: (code) => controller.setUserData('emailCode', code),
          onSendCode: () => controller.setUserData('emailCode', ''), // 重新发送
          isLoading: controller.isLoading,
        );
        
      case RegisterStep.username:
      case RegisterStep.password:
      case RegisterStep.confirmPassword:
      case RegisterStep.nickname:
      case RegisterStep.gender:
        return PersonalInfoStep(
          userData: controller.userData,
          onDataChanged: controller.setUserData,
        );
        
      case RegisterStep.region:
      case RegisterStep.courses:
        return CourseSelectionStep(
          selectedCourses: controller.userData['selectedCourses'] ?? [],
          onCoursesChanged: (courses) => controller.setUserData('selectedCourses', courses),
          isLoading: controller.isLoading,
        );
    }
  }

  // 深色沉浸式背景 - 与登录页面完全相同
  Widget _buildDarkBackground() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF000000), // 纯黑
            Color(0xFF0E0E0E), // 深黑
            Color(0xFF1a1a1a), // 深灰
            Color(0xFF2a2a2a), // 中灰
            Color(0xFF0E0E0E), // 深黑
            Color(0xFF000000), // 纯黑
          ],
          stops: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
        ),
      ),
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0x15222222),
              Color(0x08333333),
              Color(0x10111111),
              Color(0x05222222),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Container(
          decoration: const BoxDecoration(
            gradient: RadialGradient(
              center: Alignment(0.8, -0.8),
              radius: 1.8,
              colors: [
                Color(0x08FFFFFF),
                Color(0x04E0E0E0),
                Color(0x02CCCCCC),
                Colors.transparent,
              ],
              stops: [0.0, 0.3, 0.6, 1.0],
            ),
          ),
        ),
      ),
    );
  }

  // 简化的深色导航栏 - 只保留返回按钮，移除标题
  Widget _buildCustomNavigationBar(BuildContext context, RegisterController controller) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // 返回按钮
          if (controller.currentStep.index > 0)
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                HapticFeedback.selectionClick();
                controller.previousStep();
              },
              child: Icon(
                CupertinoIcons.back,
                color: Colors.white.withValues(alpha: 0.9),
                size: 28,
              ),
            )
          else
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () {
                HapticFeedback.selectionClick();
                Navigator.pop(context);
              },
              child: Icon(
                CupertinoIcons.xmark,
                color: Colors.white.withValues(alpha: 0.7),
                size: 24,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons(BuildContext context, RegisterController controller) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 下一步/完成按钮 - 深色主题适配
          SizedBox(
            width: double.infinity,
            child: CupertinoButton(
              padding: const EdgeInsets.symmetric(vertical: 16),
              onPressed: controller.canProceedToNext && !controller.isLoading
                  ? () => controller.nextStep()
                  : null,
              color: controller.canProceedToNext && !controller.isLoading
                  ? Colors.white.withValues(alpha: 0.9)
                  : Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
              child: controller.isLoading
                  ? const CupertinoActivityIndicator(color: Colors.black)
                  : Text(
                      _getButtonText(controller.currentStep),
                      style: AppFonts.createMixedStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // 已有账号登录 - 深色主题适配
          CupertinoButton(
            onPressed: () => Navigator.pushReplacementNamed(context, '/login'),
            child: Text(
              '已有账号？立即登录',
              style: AppFonts.createMixedStyle(
                fontSize: 16,
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getButtonText(RegisterStep step) {
    switch (step) {
      case RegisterStep.phone:
        return '发送验证码';
      case RegisterStep.phoneVerification:
        return '验证手机号';
      case RegisterStep.email:
        return '发送验证码';
      case RegisterStep.emailVerification:
        return '验证邮箱';
      case RegisterStep.courses:
        return '完成注册';
      default:
        return '下一步';
    }
  }
}