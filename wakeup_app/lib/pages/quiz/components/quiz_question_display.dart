import 'package:flutter/cupertino.dart';
import '../../../models/quiz_model.dart';
import '../../../constants/fonts.dart';

/// 问题显示组件
/// 从原有的4494行traditional_quiz_page中提取出来
class QuizQuestionDisplay extends StatelessWidget {
  final Question question;
  final VoidCallback? onFavoriteToggle;
  final bool isFavorited;
  final int currentIndex;
  final int totalQuestions;

  const QuizQuestionDisplay({
    super.key,
    required this.question,
    this.onFavoriteToggle,
    this.isFavorited = false,
    required this.currentIndex,
    required this.totalQuestions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 问题头部信息
          _buildQuestionHeader(),
          const SizedBox(height: 16),
          
          // 问题内容
          _buildQuestionContent(),
          
          // 问题图片（如果有）
          // Note: 图片功能暂时移除，可以后续根据需要添加
          // if (question.imageUrl != null) ...[
          //   const SizedBox(height: 16),
          //   _buildQuestionImage(),
          // ],
        ],
      ),
    );
  }

  /// 构建问题头部信息
  Widget _buildQuestionHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 问题编号
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: CupertinoColors.systemBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            '${currentIndex + 1}/$totalQuestions',
            style: AppFonts.createMixedStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: CupertinoColors.systemBlue,
            ),
          ),
        ),
        
        // 收藏按钮
        CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: onFavoriteToggle,
          child: Icon(
            isFavorited 
                ? CupertinoIcons.heart_fill 
                : CupertinoIcons.heart,
            color: isFavorited 
                ? CupertinoColors.systemRed 
                : CupertinoColors.systemGrey,
            size: 24,
          ),
        ),
      ],
    );
  }

  /// 构建问题内容
  Widget _buildQuestionContent() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: CupertinoColors.systemGrey5,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 问题内容
          Text(
            question.content,
            style: AppFonts.createMixedStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: CupertinoColors.label,
              height: 1.5,
            ),
          ),
          
          // 问题类型标签
          const SizedBox(height: 12),
          _buildQuestionTypeTag(),
        ],
      ),
    );
  }

  /// 构建问题类型标签
  Widget _buildQuestionTypeTag() {
    final typeConfig = _getQuestionTypeConfig(question.type);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: typeConfig.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        typeConfig.label,
        style: AppFonts.createMixedStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: typeConfig.color,
        ),
      ),
    );
  }


  /// 获取问题类型配置
  _QuestionTypeConfig _getQuestionTypeConfig(QuestionType type) {
    switch (type) {
      case QuestionType.singleChoice:
      case QuestionType.multipleChoice:
        return _QuestionTypeConfig('选择题', CupertinoColors.systemBlue);
      case QuestionType.trueFalse:
        return _QuestionTypeConfig('判断题', CupertinoColors.systemRed);
      case QuestionType.fillBlank:
        return _QuestionTypeConfig('填空题', CupertinoColors.systemGreen);
      case QuestionType.shortAnswer:
        return _QuestionTypeConfig('简答题', CupertinoColors.systemOrange);
      case QuestionType.matching:
        return _QuestionTypeConfig('匹配题', CupertinoColors.systemPurple);
      case QuestionType.ordering:
        return _QuestionTypeConfig('排序题', CupertinoColors.systemTeal);
      case QuestionType.programming:
        return _QuestionTypeConfig('编程题', CupertinoColors.systemIndigo);
      case QuestionType.caseAnalysis:
        return _QuestionTypeConfig('案例题', CupertinoColors.systemBrown);
      case QuestionType.calculation:
        return _QuestionTypeConfig('计算题', CupertinoColors.systemMint);
    }
  }
}

/// 问题类型配置
class _QuestionTypeConfig {
  final String label;
  final Color color;

  _QuestionTypeConfig(this.label, this.color);
}