import 'package:flutter/foundation.dart';
import '../../../models/quiz_model.dart';
import '../../../core/services/api_client.dart';
import '../../../core/utils/app_exceptions.dart';
import '../../../constants/quiz_constants.dart';

/// Quiz状态控制器
/// 从原有的traditional_quiz_page中提取状态管理逻辑
class QuizStateController extends ChangeNotifier {
  // 基础状态
  List<Question> _questions = [];
  int _currentQuestionIndex = 0;
  bool _isLoading = false;
  String? _errorMessage;
  
  // 答题状态
  final Map<int, int> _userAnswers = {}; // questionId -> optionId
  final Map<int, bool> _favoritedQuestions = {}; // questionId -> isFavorited
  QuestionInteractionState _interactionState = QuestionInteractionState.waitingForSelection;
  
  // 计时器状态
  int _currentQuestionTime = 0;
  int _totalTime = 0;

  // Getters
  List<Question> get questions => _questions;
  int get currentQuestionIndex => _currentQuestionIndex;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Map<int, int> get userAnswers => _userAnswers;
  Map<int, bool> get favoritedQuestions => _favoritedQuestions;
  QuestionInteractionState get interactionState => _interactionState;
  int get currentQuestionTime => _currentQuestionTime;
  int get totalTime => _totalTime;
  
  Question? get currentQuestion => 
      _questions.isNotEmpty && _currentQuestionIndex < _questions.length
          ? _questions[_currentQuestionIndex]
          : null;
          
  int? get selectedOptionId => 
      currentQuestion != null ? _userAnswers[currentQuestion!.id] : null;
      
  bool get isCurrentQuestionFavorited => 
      currentQuestion != null ? (_favoritedQuestions[currentQuestion!.id] ?? false) : false;

  bool get canGoToNext => _currentQuestionIndex < _questions.length - 1;
  bool get canGoToPrevious => _currentQuestionIndex > 0;
  
  int get answeredQuestionsCount => _userAnswers.length;
  double get progress => _questions.isEmpty 
      ? 0.0 
      : (_currentQuestionIndex + 1) / _questions.length;

  /// 加载问题列表
  Future<void> loadQuestions(int courseId, {List<int>? questionIds}) async {
    _setLoading(true);
    _setError(null);
    
    try {
      final apiClient = ApiClient();
      final endpoint = questionIds != null && questionIds.isNotEmpty
          ? '/api/questions/batch'
          : '/api/courses/$courseId/questions';
      
      final body = questionIds != null ? {'question_ids': questionIds} : null;
      
      final response = await (questionIds != null 
          ? apiClient.post<Map<String, dynamic>>(endpoint, body: body)
          : apiClient.get<Map<String, dynamic>>(endpoint));
      
      if (response.isSuccess) {
        final responseData = response.data as Map<String, dynamic>;
        final questionsData = responseData['data'] as List<dynamic>;
        _questions = questionsData
            .map((json) => Question.fromJson(json))
            .toList();
        
        if (_questions.isEmpty) {
          throw ApiException('没有找到相关题目');
        }
        
        _currentQuestionIndex = 0;
        _resetAnswers();
        
        notifyListeners();
      } else {
        throw response.error!;
      }
    } catch (e) {
      // 提供更详细的错误信息
      String errorMessage;
      if (e.toString().contains('timeout') || e.toString().contains('TimeoutException')) {
        errorMessage = '请求超时，请检查网络连接后重试';
      } else if (e.toString().contains('SocketException') || e.toString().contains('NetworkException')) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else if (e.toString().contains('FormatException') || e.toString().contains('解析')) {
        errorMessage = '数据格式错误，请联系技术支持';
      } else if (e.toString().contains('404')) {
        errorMessage = '未找到相关题目，该课程可能暂无题库';
      } else if (e.toString().contains('500')) {
        errorMessage = '服务器内部错误，请稍后再试';
      } else {
        errorMessage = '加载失败: ${e.toString()}';
      }
      
      debugPrint('❌ QuizStateController加载题目失败: $e');
      _setError(errorMessage);
    } finally {
      _setLoading(false);
    }
  }

  /// 选择选项
  void selectOption(int optionId) {
    if (currentQuestion == null || _interactionState != QuestionInteractionState.waitingForSelection) {
      return;
    }
    
    _userAnswers[currentQuestion!.id] = optionId;
    
    // 判断答案是否正确
    final selectedOption = currentQuestion!.options.firstWhere(
      (option) => option.id == optionId,
      orElse: () => currentQuestion!.options.first,
    );
    
    _interactionState = selectedOption.isCorrect 
        ? QuestionInteractionState.answerCorrect 
        : QuestionInteractionState.answerIncorrect;
    notifyListeners();
  }

  /// 下一题
  void nextQuestion() {
    if (canGoToNext) {
      _currentQuestionIndex++;
      if (_userAnswers.containsKey(currentQuestion!.id)) {
        // 根据之前的答案设置状态
        final selectedOptionId = _userAnswers[currentQuestion!.id]!;
        final selectedOption = currentQuestion!.options.firstWhere(
          (option) => option.id == selectedOptionId,
          orElse: () => currentQuestion!.options.first,
        );
        _interactionState = selectedOption.isCorrect 
            ? QuestionInteractionState.answerCorrect 
            : QuestionInteractionState.answerIncorrect;
      } else {
        _interactionState = QuestionInteractionState.waitingForSelection;
      }
      _currentQuestionTime = 0;
      notifyListeners();
    }
  }

  /// 上一题
  void previousQuestion() {
    if (canGoToPrevious) {
      _currentQuestionIndex--;
      if (_userAnswers.containsKey(currentQuestion!.id)) {
        // 根据之前的答案设置状态
        final selectedOptionId = _userAnswers[currentQuestion!.id]!;
        final selectedOption = currentQuestion!.options.firstWhere(
          (option) => option.id == selectedOptionId,
          orElse: () => currentQuestion!.options.first,
        );
        _interactionState = selectedOption.isCorrect 
            ? QuestionInteractionState.answerCorrect 
            : QuestionInteractionState.answerIncorrect;
      } else {
        _interactionState = QuestionInteractionState.waitingForSelection;
      }
      _currentQuestionTime = 0;
      notifyListeners();
    }
  }

  /// 跳转到指定题目
  void goToQuestion(int index) {
    if (index >= 0 && index < _questions.length) {
      _currentQuestionIndex = index;
      if (_userAnswers.containsKey(currentQuestion!.id)) {
        // 根据之前的答案设置状态
        final selectedOptionId = _userAnswers[currentQuestion!.id]!;
        final selectedOption = currentQuestion!.options.firstWhere(
          (option) => option.id == selectedOptionId,
          orElse: () => currentQuestion!.options.first,
        );
        _interactionState = selectedOption.isCorrect 
            ? QuestionInteractionState.answerCorrect 
            : QuestionInteractionState.answerIncorrect;
      } else {
        _interactionState = QuestionInteractionState.waitingForSelection;
      }
      _currentQuestionTime = 0;
      notifyListeners();
    }
  }

  /// 切换收藏状态
  void toggleFavorite() {
    if (currentQuestion == null) return;
    
    final questionId = currentQuestion!.id;
    _favoritedQuestions[questionId] = !(_favoritedQuestions[questionId] ?? false);
    notifyListeners();
    
    // TODO: 调用API保存收藏状态
    _saveFavorite(questionId, _favoritedQuestions[questionId]!);
  }

  /// 更新计时器
  void updateTimer() {
    _currentQuestionTime++;
    _totalTime++;
    notifyListeners();
  }

  /// 重置到等待选择状态
  void resetToWaitingState() {
    _interactionState = QuestionInteractionState.waitingForSelection;
    notifyListeners();
  }

  /// 提交答案（用于简答题、填空题等）
  void submitAnswer(String answer) {
    if (currentQuestion == null) return;
    
    // TODO: 处理非选择题的答案提交
    // 简单设为正确状态，实际应该根据答案判断
    _interactionState = QuestionInteractionState.answerCorrect;
    notifyListeners();
  }

  /// 获取答题统计
  Map<String, dynamic> getStatistics() {
    int correctCount = 0;
    
    for (final question in _questions) {
      final selectedOptionId = _userAnswers[question.id];
      if (selectedOptionId != null) {
        final selectedOption = question.options.firstWhere(
          (option) => option.id == selectedOptionId,
          orElse: () => question.options.first,
        );
        if (selectedOption.isCorrect) {
          correctCount++;
        }
      }
    }
    
    return {
      'totalQuestions': _questions.length,
      'answeredQuestions': _userAnswers.length,
      'correctAnswers': correctCount,
      'accuracy': _userAnswers.isEmpty ? 0.0 : correctCount / _userAnswers.length,
      'totalTime': _totalTime,
      'averageTimePerQuestion': _userAnswers.isEmpty ? 0 : _totalTime / _userAnswers.length,
    };
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _resetAnswers() {
    _userAnswers.clear();
    _favoritedQuestions.clear();
    _interactionState = QuestionInteractionState.waitingForSelection;
    _currentQuestionTime = 0;
    _totalTime = 0;
  }

  Future<void> _saveFavorite(int questionId, bool isFavorited) async {
    try {
      final apiClient = ApiClient();
      await apiClient.post(
        '/api/questions/$questionId/favorite',
        body: {'is_favorited': isFavorited},
      );
    } catch (e) {
      // 静默失败，不影响用户体验
      debugPrint('保存收藏状态失败: $e');
    }
  }

}

