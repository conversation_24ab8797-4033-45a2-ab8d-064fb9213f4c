import 'dart:io' show Platform;

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import '../constants/fonts.dart';
import '../widgets/back_button.dart';


class LeaderboardPage extends StatefulWidget {
  const LeaderboardPage({super.key});

  @override
  State<LeaderboardPage> createState() => _LeaderboardPageState();
}

class _LeaderboardPageState extends State<LeaderboardPage>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  String _selectedRankingType = "world"; // "world" or "friends"

  bool _isWorldLoading = true;
  List<Map<String, dynamic>> _worldLeaderboardData = [];
  bool _isFriendsLoading = true;
  List<Map<String, dynamic>> _friendsLeaderboardData = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController!.addListener(() {
      final newIndex = _tabController!.index;
      if (newIndex == 0 && _selectedRankingType != "world") {
        setState(() {
          _selectedRankingType = "world";
        });
      } else if (newIndex == 1 && _selectedRankingType != "friends") {
        setState(() {
          _selectedRankingType = "friends";
        });
      }
    });
    _loadLeaderboardData("world");
    _loadLeaderboardData("friends");
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _loadLeaderboardData(String rankingType) async {
    if (rankingType == "world") {
      setState(() {
        _isWorldLoading = true;
      });
    } else {
      // friends
      setState(() {
        _isFriendsLoading = true;
      });
    }

    // 模拟加载数据
    await Future.delayed(Duration(seconds: 1));

    List<Map<String, dynamic>> data = [];

    if (rankingType == "world") {
      // 生成世界排行数据
      for (int i = 1; i <= 20; i++) {
        int score = 500 - (i * 20) + (i % 3 == 0 ? 10 : 0);
        final bool isCurrentUser = i == 5;
        data.add({
          'rank': i,
          'name': isCurrentUser ? '我' : '全球玩家${200 + i}',
          'avatar': 'assets/images/avatar_${(i % 5) + 1}.png',
          'score': score,
          'isCurrentUser': isCurrentUser,
        });
      }
    } else if (rankingType == "friends") {
      // 生成好友排行数据
      for (int i = 1; i <= 10; i++) {
        int score = 480 - (i * 15) + (i % 2 == 0 ? 5 : 0);
        final bool isCurrentUser = i == 3;
        data.add({
          'rank': i,
          'name': isCurrentUser ? '我' : '好友$i',
          'avatar': 'assets/images/avatar_${((i + 2) % 5) + 1}.png',
          'score': score,
          'isCurrentUser': isCurrentUser,
        });
      }
      // Optional: Add current user if not in top N friends for friends list
      // if (!data.any((user) => user['isCurrentUser'] == true) && data.isNotEmpty) {
      //   data.add({ /* current user data */ });
      // }
    }

    setState(() {
      if (rankingType == "world") {
        _worldLeaderboardData = data;
        _isWorldLoading = false;
      } else {
        // friends
        _friendsLeaderboardData = data;
        _isFriendsLoading = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/traditional_quiz_bg.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPageForRankType("world"),
                    _buildPageForRankType("friends"),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          BackButtonWidget(
            text: "",
            color: Colors.white,
            onPressed: () => Navigator.of(context).pop(),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              "日排行榜",
              style:
                  Platform.isIOS &&
                          Localizations.localeOf(context).languageCode != 'zh'
                      ? TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        letterSpacing: -0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.7),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      )
                      : AppFonts.createTitleStyle(
                        fontSize: 18,
                        isLatinText:
                            Localizations.localeOf(context).languageCode !=
                            'zh',
                      ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          // 平衡布局的空白区域，保持与返回按钮宽度大致对称
          SizedBox(width: 34 + 16), // 34 for button, 16 for SizedBox
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      tabs: [Tab(text: "世界排行"), Tab(text: "好友排行")],
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
      indicatorColor: Colors.white,
      indicatorWeight: 3.0,
      indicatorSize: TabBarIndicatorSize.label,
      labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: 15,
      ),
    );
  }

  Widget _buildPageForRankType(String rankingType) {
    bool isLoading;
    List<Map<String, dynamic>> specificData;

    if (rankingType == "world") {
      isLoading = _isWorldLoading;
      specificData = _worldLeaderboardData;
    } else {
      // friends
      isLoading = _isFriendsLoading;
      specificData = _friendsLeaderboardData;
    }

    if (isLoading) {
      return Center(child: CupertinoActivityIndicator());
    }
    if (specificData.isEmpty) {
      // _buildEmptyState uses _selectedRankingType, which is synced by the TabController listener.
      // So, it will display the correct message for the active tab.
      return _buildEmptyState();
    }
    return _buildLeaderboardList(
      specificData,
    ); // Pass specific data to the list builder
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            CupertinoIcons.chart_bar,
            size: 60,
            color: Colors.white.withValues(alpha: 0.6),
          ),
          SizedBox(height: 16),
          Text(
            _selectedRankingType == "friends" ? "暂无好友排行数据" : "暂无排行数据",
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            _selectedRankingType == "friends" ? "邀请好友一起学习吧！" : "继续学习提高排名吧",
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboardList(
    List<Map<String, dynamic>> currentLeaderboardData,
  ) {
    // 找出前三名展示在顶部
    final topThree = currentLeaderboardData.take(3).toList();
    final otherRanks = currentLeaderboardData.skip(3).toList();

    return Column(
      children: [
        // 顶部前三名展示
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // 第二名
              if (topThree.length > 1) _buildTopAvatar(topThree[1], 2, 70),

              // 第一名 - 中间且最大
              SizedBox(width: 12),
              if (topThree.isNotEmpty) _buildTopAvatar(topThree[0], 1, 90),
              SizedBox(width: 12),

              // 第三名
              if (topThree.length > 2) _buildTopAvatar(topThree[2], 3, 60),
            ],
          ),
        ),

        // 其余排名列表
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: otherRanks.length,
            itemBuilder: (context, index) {
              final item = otherRanks[index];
              return _buildRankItem(item);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTopAvatar(Map<String, dynamic> item, int rank, double size) {
    final Color borderColor =
        rank == 1
            ? Colors.amber
            : rank == 2
            ? Colors.grey.shade300
            : Colors.brown.shade300;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          alignment: Alignment.bottomRight,
          children: [
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: borderColor, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: borderColor.withValues(alpha: 0.5),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  item['avatar'],
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.purple.withValues(alpha: 0.3),
                      child: Icon(
                        CupertinoIcons.person_fill,
                        color: Colors.white,
                        size: size / 2,
                      ),
                    );
                  },
                ),
              ),
            ),
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    rank == 1
                        ? Colors.amber
                        : rank == 2
                        ? Colors.grey.shade300
                        : Colors.brown.shade300,
              ),
              child: Center(
                child: Text(
                  "$rank",
                  style: TextStyle(
                    color: rank == 1 ? Colors.black : Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Text(
          item['name'],
          style: TextStyle(
            color: item['isCurrentUser'] ? Colors.amber : Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4),
        Text(
          "${item['score']}分",
          style: TextStyle(color: Colors.white.withValues(alpha: 0.7), fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildRankItem(Map<String, dynamic> item) {
    final bool isCurrentUser = item['isCurrentUser'] == true;

    return Container(
      margin: EdgeInsets.only(bottom: 10),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color:
            isCurrentUser
                ? Colors.purple.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isCurrentUser
                  ? Colors.purple.withValues(alpha: 0.6)
                  : Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 排名
          Container(
            width: 24,
            alignment: Alignment.center,
            child: Text(
              "${item['rank']}",
              style: TextStyle(
                color: isCurrentUser ? Colors.amber : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(width: 12),

          // 头像
          ClipOval(
            child: SizedBox(
              width: 36,
              height: 36,
              child: Image.asset(
                item['avatar'],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return ColoredBox(
                    color: Colors.purple.withValues(alpha: 0.3),
                    child: Icon(
                      CupertinoIcons.person_fill,
                      color: Colors.white,
                      size: 20,
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(width: 12),

          // 用户名
          Text(
            item['name'],
            style: TextStyle(
              color: isCurrentUser ? Colors.amber : Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Spacer(),

          // 分数
          Text(
            "${item['score']}分",
            style: TextStyle(
              color: isCurrentUser ? Colors.amber : Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
