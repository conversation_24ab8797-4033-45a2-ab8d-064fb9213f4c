import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wakeup_app/l10n/app_localizations.dart';
import '../../providers/user_provider.dart';
import '../../providers/locale_provider.dart';
import '../../services/auth_service.dart';
// import '../auth/login_page.dart';
// import 'package:flutter/cupertino.dart';
import '../../widgets/auth_wrapper.dart';
import '../../core/routing/app_router.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  Map<String, dynamic>? userInfo;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn) {
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final info = await AuthService.getUserInfo(
        userProvider.userId,
        userProvider.token ?? '',
      );
      if (!mounted) return;
      setState(() {
        userInfo = info;
        isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('获取用户信息失败: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final isLoggedIn = userProvider.isLoggedIn;

    // 使用 AuthWrapper 包装页面
    return AuthWrapper(
      child: Scaffold(
        body: SafeArea(
          child:
              isLoggedIn
                  ? _buildLoggedInView(userProvider)
                  : _buildNotLoggedInView(),
        ),
      ),
    );
  }

  Widget _buildLoggedInView(UserProvider userProvider) {
    return RefreshIndicator(
      onRefresh: _loadUserInfo,
      child: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          const Text(
            '个人中心',
            style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          _buildUserInfoCard(userProvider),
          const SizedBox(height: 20),
          const ProfileSectionHeader(title: '学习数据'),
          _buildStatsCard(),
          const SizedBox(height: 20),
          const ProfileSectionHeader(title: '设置'),
          _buildSettingsSection(),
          const SizedBox(height: 40),
          Center(
            child: ElevatedButton.icon(
              onPressed: () {
                showDialog(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: const Text('确认退出'),
                        content: const Text('确定要退出登录吗？'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('取消'),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                              userProvider.clearUser();
                              // 使用统一路由处理登出
                              AppRouter.toLogin(context);
                            },
                            child: const Text(
                              '确认',
                              style: TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                );
              },
              icon: const Icon(Icons.logout, color: Colors.red),
              label: const Text('退出登录', style: TextStyle(color: Colors.red)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.withValues(alpha: 0.1),
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoCard(UserProvider userProvider) {
    return Card(
      elevation: 4,
      margin: EdgeInsets.zero,
      color: Colors.grey.shade900,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade800,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.purple, width: 2),
                  ),
                  child: const Icon(
                    Icons.person,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userInfo?['name'] ?? '用户${userProvider.userId}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        userInfo?['phone'] ?? 'ID: ${userProvider.userId}',
                        style: TextStyle(color: Colors.grey.shade400),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.purple.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.purple),
                        ),
                        child: const Text(
                          '普通会员',
                          style: TextStyle(color: Colors.purple),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (isLoading)
              const Padding(
                padding: EdgeInsets.only(top: 16.0),
                child: Center(child: CircularProgressIndicator()),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard() {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(top: 8),
      color: Colors.grey.shade900,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('课程', userInfo?['course_count'] ?? 0),
            const VerticalDivider(
              color: Colors.grey,
              thickness: 1,
              indent: 10,
              endIndent: 10,
            ),
            _buildStatItem('完成', userInfo?['completed_count'] ?? 0),
            const VerticalDivider(
              color: Colors.grey,
              thickness: 1,
              indent: 10,
              endIndent: 10,
            ),
            _buildStatItem('学时', userInfo?['study_hours'] ?? 0),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, int value) {
    return Column(
      children: [
        Text(
          '$value',
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.lightBlue,
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(color: Colors.grey.shade400)),
      ],
    );
  }

  Widget _buildSettingsSection() {
    final localeProvider = Provider.of<LocaleProvider>(context);
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(top: 8),
      color: Colors.grey.shade900,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          _buildSettingItem(
            icon: Icons.language,
            title: '语言设置',
            subtitle:
                localeProvider.locale.languageCode == 'zh' ? '中文' : 'English',
            trailing: Switch(
              value: localeProvider.locale.languageCode == 'zh',
              onChanged: (value) {
                localeProvider.setLocale(
                  value ? const Locale('zh') : const Locale('en'),
                );
              },
            ),
          ),
          const Divider(height: 1, color: Colors.grey),
          _buildSettingItem(
            icon: Icons.notifications,
            title: '通知设置',
            subtitle: '管理您的通知偏好',
            onTap: () {},
          ),
          const Divider(height: 1, color: Colors.grey),
          _buildSettingItem(
            icon: Icons.privacy_tip,
            title: '隐私设置',
            subtitle: '管理您的隐私选项',
            onTap: () {},
          ),

          // 添加演示部分
          const Divider(height: 1, color: Colors.grey),
          _buildDemoSection(),
        ],
      ),
    );
  }

  // 添加演示部分
  Widget _buildDemoSection() {
    return ExpansionTile(
      leading: const Icon(Icons.science, color: Colors.blue),
      title: const Text('演示部分', style: TextStyle(fontWeight: FontWeight.bold)),
      subtitle: const Text('查看UI组件演示'),
      childrenPadding: const EdgeInsets.symmetric(vertical: 8.0),
      children: [
        _buildDemoItem(
          title: 'Apple Music 风格滚动标题',
          subtitle: '展示类似Apple Music的动态标题效果',
          icon: Icons.view_day,
          color: Colors.purple,
          onTap:
              () =>
                  Navigator.of(context).pushNamed('/dynamic_shrinking_header'),
        ),
      ],
    );
  }

  Widget _buildDemoItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withValues(alpha: 0.2),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  // 添加设置项组件
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.white70),
      title: Text(title),
      subtitle:
          subtitle != null
              ? Text(subtitle, style: TextStyle(color: Colors.grey.shade400))
              : null,
      trailing:
          trailing ??
          (onTap != null
              ? const Icon(Icons.arrow_forward_ios, size: 16)
              : null),
      onTap: onTap,
    );
  }

  Widget _buildNotLoggedInView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.account_circle, size: 120, color: Colors.grey),
          const SizedBox(height: 24),
          Text(
            AppLocalizations.of(context)!.pleaseLoginFirst,
            style: const TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              AppRouter.pushLogin(context);
            },
            child: Text(AppLocalizations.of(context)!.login),
          ),
        ],
      ),
    );
  }
}

class ProfileSectionHeader extends StatelessWidget {
  final String title;

  const ProfileSectionHeader({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white70,
        ),
      ),
    );
  }
}
