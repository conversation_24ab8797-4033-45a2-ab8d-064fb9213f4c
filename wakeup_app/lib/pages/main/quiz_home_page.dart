import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math' as math;
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:io';
import 'package:wakeup_app/l10n/app_localizations.dart';

import '../../constants/fonts.dart';
import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../../services/preload_service.dart';
import '../../widgets/shimmer_loading.dart';
import '../../widgets/user_avatar_button.dart';
import '../../widgets/liquid_glass_avatar_widget.dart';
import '../../widgets/gradient_course_card.dart';
// import '../../utils/page_transitions.dart';
// import '../quiz/quiz_attempt_page.dart' as quiz;

import '../auth/login_page.dart';
// import 'main_page.dart';
import '../../core/routing/app_router.dart';

class LearningPage extends StatefulWidget {
  const LearningPage({super.key});

  @override
  State<LearningPage> createState() => _LearningPageState();
}

class _LearningPageState extends State<LearningPage>
    with TickerProviderStateMixin {
  Future<List<Map<String, dynamic>>>? _userCourses;
  bool _isLoading = false;
  String? _errorMessage;

  // 当前选中的课程索引
  int _currentCourseIndex = 0;

  // 滑动控制器
  late PageController _pageController;

  // 我的课程卡片滑动控制器 - 在声明时直接初始化，避免LateInitializationError
  final PageController _courseCardController = PageController(
    viewportFraction: 0.85, // 使用0.85的视口比例，确保卡片居中显示
    initialPage: 0,
  );

  // 动画控制器（用于滑块动画）
  late AnimationController _slideController;

  // 滑动处理函数
  bool _isSliding = false;

  // 手势跟随相关变量
  double _dragProgress = 0.0;
  final double _slideThreshold = 0.55; // 滑动完成阈值（55%）
  bool _isDragging = false;

  // 拖拽起始位置和当前偏移量
  double _startDragPosition = 0.0;
  double _currentSlideOffset = 0.0;

  // 预加载服务实例
  final PreloadService _preloadService = PreloadService();

  @override
  void initState() {
    super.initState();

    // 初始化纹理
    NoiseTexture.initialize();

    // 初始化页面控制器，用于视差效果
    _pageController = PageController(
      viewportFraction: 0.85,
      initialPage: _currentCourseIndex,
    );

    // 已在声明时初始化了_courseCardController，这里不需要再次初始化

    // 给课程卡片控制器添加页面变化监听器
    _courseCardController.addListener(() {
      // 当页面滑动停止时更新当前选中的课程索引
      if (_courseCardController.position.isScrollingNotifier.value == false &&
          _courseCardController.page != null) {
        final int newIndex = _courseCardController.page!.round();
        if (newIndex != _currentCourseIndex) {
          setState(() {
            _currentCourseIndex = newIndex;
          });
          // 确保卡片精确对齐到整数位置
          _courseCardController.animateToPage(
            newIndex,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOut,
          );
        }
      }
    });

    // 初始化滑块动画控制器（保留用于回弹动画）
    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // 延迟加载，确保Provider可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCourses();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 重置滑块状态，确保每次页面显示时滑块都在初始位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _resetSliderState();
      }
    });

    // 检查用户课程数据是否发生变化 - 使用listen: true确保能监听到变化
    final userProvider = Provider.of<UserProvider>(context, listen: true);
    if (userProvider.courseChanged) {
      debugPrint("检测到课程数据变化，重新加载课程列表");
      // 重置变化标志
      userProvider.resetCourseChanged();
      // 重新加载课程
      _loadCourses();
    }
  }

  @override
  void didUpdateWidget(LearningPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当页面重新构建时，检查是否需要刷新数据
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (userProvider.courseChanged) {
      debugPrint("didUpdateWidget检测到课程数据变化，重新加载课程列表");
      userProvider.resetCourseChanged();
      _loadCourses();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _courseCardController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  // 执行滑动完成后的动作
  void _performSlideAction() {
    if (_userCourses == null) return;

    _userCourses!.then((courses) {
      // 重置滑块位置
      _resetSliderState();

      if (courses.isEmpty) {
        _navigateToCourse(); // 没有课程时跳转到课程页面
      } else {
        _navigateToQuiz(); // 有课程时跳转到答题页面
      }
    });
  }

  // 统一的滑块状态重置方法
  void _resetSliderState() {
    setState(() {
      _currentSlideOffset = 0.0;
      _dragProgress = 0.0;
      _isDragging = false;
      _isSliding = false;
    });
    _slideController.reset();
  }

  // 专门处理有课程时的滑动动作
  void _performCourseSlideAction(int courseId, String courseName) {
    // 重置滑块位置
    _resetSliderState();

    // 导航到答题页面
    _navigateToAiQuizPage(courseId, courseName);
  }

  void _loadCourses() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    // 只有登录状态才加载课程
    if (userProvider.isLoggedIn) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _userCourses = fetchUserCourses();
      });
    }
  }

  Future<List<Map<String, dynamic>>> fetchUserCourses() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    if (!userProvider.isLoggedIn) {
      return []; // 未登录返回空列表
    }

    try {
      debugPrint(
        '获取用户课程: userId=${userProvider.userId}, token=${userProvider.token}',
      );

      final response = await http.get(
        Uri.parse(
          '${AuthService.baseUrl}/api/user_course/list/${userProvider.userId}',
        ),
        headers: {
          'Authorization': 'Bearer ${userProvider.token}',
          'Content-Type': 'application/json',
        },
      );

      debugPrint(
        '获取用户课程响应: statusCode=${response.statusCode}, body=${response.body}',
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        final List<Map<String, dynamic>> courses =
            data.cast<Map<String, dynamic>>();

        // 打印解析后的课程数据，帮助调试
        debugPrint('成功获取到${courses.length}个用户课程:');
        for (int i = 0; i < courses.length; i++) {
          final course = courses[i];
          debugPrint(
            '课程 $i: id=${course['course_id']}, 名称=${course['course_name']}, 类型: ${course['course_id'].runtimeType}',
          );
        }

        // 获取完课程数据后，预加载当前课程的数据
        if (courses.isNotEmpty && userProvider.token != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // 延迟执行，确保不影响UI渲染
            _preloadCurrentCourseData(courses, userProvider.token!);
          });
        }

        return courses;
      } else if (response.statusCode == 401) {
        // 401错误特殊处理
        debugPrint('授权令牌无效，准备清除登录状态');

        // 使用延迟执行，避免在build过程中更新状态
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            userProvider.clearUser(); // 清除用户登录状态

            // 使用Cupertino风格的提示，替代ScaffoldMessenger
            showCupertinoDialog(
              context: context,
              barrierDismissible: true,
              builder: (BuildContext context) {
                return CupertinoAlertDialog(
                  title: const Text('登录已过期'),
                  content: const Text('您的登录已过期，请重新登录'),
                  actions: [
                    CupertinoDialogAction(
                      child: const Text('确定'),
                      onPressed: () {
                        Navigator.of(context).pop();
                        // 可选：导航到登录页面
                        AppRouter.pushLogin(context);
                      },
                    ),
                  ],
                );
              },
            );
          }
        });

        if (mounted) {
          setState(() {
            _errorMessage = '无效的授权令牌，请重新登录';
          });
        }
        return [];
      } else {
        String errorMessage = '服务器返回错误: ${response.statusCode}';
        try {
          final Map<String, dynamic> errorData = jsonDecode(response.body);
          if (errorData.containsKey('message')) {
            errorMessage = errorData['message'];
          }
        } catch (e) {
          // 解析错误响应失败，使用默认错误信息
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      // 捕获异常并返回空列表，让界面显示备用视图
      if (mounted) {
        setState(() {
          _errorMessage = '加载学习记录失败: $e';
        });
      }
      return [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 预加载当前选中课程的数据
  void _preloadCurrentCourseData(
    List<Map<String, dynamic>> courses,
    String token,
  ) {
    if (courses.isEmpty) return;

    // 确保索引在有效范围内
    final currentIndex = math.min(_currentCourseIndex, courses.length - 1);
    final currentCourse = courses[currentIndex];
    final courseId = currentCourse['course_id'];

    if (courseId != null) {
      debugPrint('🔄 开始预加载课程数据: courseId=$courseId');

      // 预加载分类标签
      _preloadService.preloadCategoryTabs(courseId, token);

      // 预加载问题
      _preloadService.preloadQuestions(courseId, token);
    }
  }

  // 导航到AI答题页面
  void _navigateToAiQuizPage(int courseId, String courseName) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!mounted) return;

    // 确保用户已登录
    if (!userProvider.isLoggedIn || userProvider.token == null) {
      showCupertinoToast(context, "请先登录");
      return;
    }

    // 添加轻微触觉反馈增强交互体验
    HapticFeedback.selectionClick();

    debugPrint('👉 准备导航到传统答题页面: courseId=$courseId, courseName=$courseName');

    // 开始预加载课程数据
    try {
      // 并行执行预加载操作以提高效率
      Future.wait([
        _preloadService.preloadCategoryTabs(courseId, userProvider.token!),
        _preloadService.preloadQuestions(courseId, userProvider.token!),
      ]).catchError((e) {
        debugPrint('预加载数据错误: $e');
        return <List<Object>>[]; // 确保返回类型满足 Future.wait 的需要
      });
    } catch (e) {
      debugPrint('预加载数据错误: $e');
    }

    // 使用无动画路由，立即显示答题页面并保持当前页面状态
    if (mounted) {
      debugPrint('👉 开始导航到传统答题页面');

      // 先检查并移除导航堆栈中已有的传统答题页面实例
      Navigator.of(
        context,
      ).popUntil((route) => route.settings.name != 'TraditionalQuizPage');

      // 然后再添加新的传统答题页面
      AppRouter.toQuiz(context, courseId: courseId, courseName: courseName);
    }
  }

  // 构建改进的滑块按钮
  Widget _buildImprovedSlideButton(String text, bool hasData) {
    return ImprovedSlideButton(
      text: text,
      progress: _dragProgress,
      slideOffset: _currentSlideOffset,
      onDragStart: (details) {
        if (_isSliding) return;
        setState(() {
          _isDragging = true;
          _dragProgress = 0.0;
          _startDragPosition = details.localPosition.dx;
          _currentSlideOffset = 0.0;
        });
      },
      onDragUpdate: (details) {
        if (_isSliding || !_isDragging) return;

        // 计算相对位移而非绝对位置
        double deltaX = details.localPosition.dx - _startDragPosition;
        // 滑块容器宽度：以示例的左右 24 为基准，并根据屏幕宽度自适应
        const double horizontalPadding = 24.0;
        double containerWidth =
            MediaQuery.of(context).size.width - horizontalPadding * 2;
        double sliderWidth = 60; // 滑块实际宽度
        double leftMargin = 12.0; // 左边距（示例文件内的内边距为12）
        double rightMargin = 12.0; // 右边距（与左侧一致）
        double maxSlideDistance =
            containerWidth - leftMargin - sliderWidth - rightMargin; // 最大滑动距离

        setState(() {
          _currentSlideOffset = deltaX.clamp(0.0, maxSlideDistance);
          _dragProgress = (_currentSlideOffset / maxSlideDistance).clamp(
            0.0,
            1.0,
          );
        });
      },
      onDragEnd: (details) {
        if (_isSliding || !_isDragging) return;

        setState(() {
          _isSliding = true;
        });

        if (_dragProgress >= _slideThreshold) {
          // 达到阈值，执行滑动完成动作
          if (hasData) {
            // 有课程时需要获取当前课程信息
            if (_userCourses != null) {
              _userCourses!
                  .then((courses) {
                    try {
                      if (courses.isEmpty ||
                          _currentCourseIndex >= courses.length) {
                        throw Exception('课程索引无效');
                      }

                      final currentCourse = courses[_currentCourseIndex];
                      if (!currentCourse.containsKey('course_id')) {
                        throw Exception('课程数据错误，缺少ID字段');
                      }

                      final dynamic rawCourseId = currentCourse['course_id'];
                      final int courseId =
                          rawCourseId is int
                              ? rawCourseId
                              : (rawCourseId is String
                                  ? int.tryParse(rawCourseId) ?? 0
                                  : 0);

                      if (courseId <= 0) {
                        throw Exception('课程ID无效');
                      }

                      final courseName =
                          currentCourse['course_name'] ?? '课程（测试）';
                      _performCourseSlideAction(courseId, courseName);
                    } catch (e) {
                      // 处理错误，回弹滑块
                      _resetSliderState();
                      if (mounted) {
                        showCupertinoToast(context, e.toString());
                      }
                    }
                  })
                  .catchError((error) {
                    // 处理Future错误
                    _resetSliderState();
                    if (mounted) {
                      showCupertinoToast(context, '获取课程信息失败: $error');
                    }
                  });
            } else {
              _performSlideAction(); // 备用处理方式
            }
          } else {
            _navigateToCourse(); // 无课程时导航到课程页面
          }
        } else {
          // 未达到阈值，回弹到起始位置
          _resetSliderState();
        }
      },
    );
  }

  // 显示一个临时的Cupertino风格提示（类似Toast）
  void showCupertinoToast(BuildContext context, String message) {
    // 创建一个悬浮的半透明提示，模拟Toast效果
    final overlay = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: CupertinoColors.darkBackgroundGray.withValues(
                    alpha: 0.8,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  message,
                  style: const TextStyle(color: CupertinoColors.white),
                ),
              ),
            ),
          ),
    );

    // 显示提示
    Overlay.of(context).insert(overlay);

    // 短暂延迟后移除
    Future.delayed(const Duration(milliseconds: 800), () {
      overlay.remove();
    });
  }

  // 导航到课程页面
  void _navigateToCourse() {
    // 重置滑块位置
    _slideController.reset();
    _isSliding = false;

    // 跳转到课程页面，使用统一路由
    AppRouter.toMainWithIndex(context, initialIndex: 1);
  }

  // 导航到答题页面
  void _navigateToQuiz() {
    if (_userCourses == null) return;

    _userCourses!.then((courses) {
      // 重置滑块位置
      _slideController.reset();
      _isSliding = false;

      if (courses.isEmpty || _currentCourseIndex >= courses.length) return;

      final selectedCourse = courses[_currentCourseIndex];

      // 确保course_id存在且为int类型
      if (!selectedCourse.containsKey('course_id')) {
        debugPrint('❌ 课程数据中没有course_id字段: $selectedCourse');
        // 安全地使用ScaffoldMessenger
        if (mounted &&
            context.findAncestorWidgetOfExactType<ScaffoldMessenger>() !=
                null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('课程数据错误，请重试'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final dynamic rawCourseId = selectedCourse['course_id'];
      final int courseId =
          rawCourseId is int
              ? rawCourseId
              : (rawCourseId is String ? int.tryParse(rawCourseId) ?? 0 : 0);

      if (courseId <= 0) {
        debugPrint('❌ 无效的course_id: $rawCourseId');
        // 安全地使用ScaffoldMessenger
        if (mounted &&
            context.findAncestorWidgetOfExactType<ScaffoldMessenger>() !=
                null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('课程ID无效，请重试'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final courseName = selectedCourse['course_name'] ?? '课程';

      debugPrint('✅ 导航到课程: id=$courseId, name=$courseName');
      _navigateToAiQuizPage(courseId, courseName);
    });
  }

  // 添加一个自定义的磨砂纹理Widget
  Widget _buildGradientContainer({required Widget child}) {
    return Container(
      decoration: const BoxDecoration(
        gradient: RadialGradient(
          center: Alignment(0.9, -0.9), // 右上角
          radius: 0.6,
          colors: [Color(0xFFBB0000), Colors.transparent],
          stops: [0.0, 0.9],
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.black,
              const Color(0xFF151515),
              const Color(0xFF330000),
            ],
            stops: const [0.3, 0.7, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // 右下角的红色渐变
            Positioned(
              right: 0,
              bottom: 0,
              width: MediaQuery.of(context).size.width * 1.0,
              height: MediaQuery.of(context).size.height * 0.7,
              child: Container(
                decoration: const BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(0.6, 0.6),
                    radius: 1.0,
                    colors: [Color(0xCCCC0000), Colors.transparent],
                    stops: [0.0, 0.9],
                  ),
                ),
              ),
            ),
            // 内容层
            BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 1.0, sigmaY: 1.0),
              child: child,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotLoggedInView() {
    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.lock_outline, size: 64, color: Colors.white),
                  const SizedBox(height: 24),
                  Text(
                    AppLocalizations.of(context)!.pleaseLoginToLearn,
                    style: AppFonts.createMixedStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      final navigator = Navigator.of(context);
                      navigator
                          .push(
                            CupertinoPageRoute(
                              builder: (context) => const LoginPage(),
                            ),
                          )
                          .then((_) {
                            if (!mounted) return;
                            // 登录后重新加载
                            if (Provider.of<UserProvider>(
                              context,
                              listen: false,
                            ).isLoggedIn) {
                              _loadCourses();
                            }
                          });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFAA0000),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.loginNow,
                      style: AppFonts.createMixedStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 添加用户头像按钮在右上角
            Positioned(top: 8, right: 16, child: UserAvatarButton(size: 36)),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage ?? "加载失败",
                    style: AppFonts.bodyStyle,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _loadCourses,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFAA0000),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.retry,
                      style: AppFonts.subheadingStyle,
                    ),
                  ),
                ],
              ),
            ),

            // 添加用户头像按钮在右上角
            Positioned(top: 8, right: 16, child: UserAvatarButton(size: 36)),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            const LearningPageShimmer(),

            // 添加用户头像按钮在右上角
            Positioned(top: 8, right: 16, child: UserAvatarButton(size: 36)),
          ],
        ),
      ),
    );
  }

  Widget _buildMainView(List<Map<String, dynamic>> courses) {
    if (courses.isEmpty) {
      return _buildGradientContainer(
        child: Stack(
          children: [
            // 主要内容
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题栏和用户头像
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24.0,
                        vertical: 16.0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.noCourses,
                            style: AppFonts.createMixedStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          // 用户头像按钮已被移除，因为使用了PageTitleBar
                        ],
                      ),
                    ),

                    Expanded(child: Container()), // 填充顶部空间
                    // 内容区域
                    Padding(
                      padding: const EdgeInsets.only(bottom: 24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 标题和提示信息
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24.0,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Icon(
                                  Icons.school_outlined,
                                  size: 64,
                                  color: Colors.white,
                                ),
                                const SizedBox(height: 15), // 从24减少到15
                                Text(
                                  AppLocalizations.of(context)!.noCourses,
                                  style: AppFonts.createMixedStyle(
                                    fontSize: 36,
                                    fontWeight: FontWeight.w900,
                                    color: Colors.white,
                                  ).copyWith(
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withValues(
                                          alpha: 0.5,
                                        ),
                                        offset: const Offset(0, 1),
                                        blurRadius: 1,
                                      ),
                                      Shadow(
                                        color: Colors.white.withValues(
                                          alpha: 0.2,
                                        ),
                                        offset: const Offset(0, -0.5),
                                        blurRadius: 0,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 5), // 从10减少到5
                                SizedBox(
                                  width: MediaQuery.of(context).size.width - 48,
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      AppLocalizations.of(
                                        context,
                                      )!.selectCourseInterest,
                                      style: TextStyle(
                                        fontFamily:
                                            AppFonts.platformChineseFont,
                                        fontSize: 32,
                                        fontWeight: FontWeight.bold,
                                        foreground:
                                            Paint()
                                              ..shader = LinearGradient(
                                                colors: [
                                                  const Color(0xFFFF3333),
                                                  const Color(0xFF990000),
                                                ],
                                              ).createShader(
                                                const Rect.fromLTWH(
                                                  0.0,
                                                  0.0,
                                                  200.0,
                                                  70.0,
                                                ),
                                              ),
                                        shadows: [
                                          Shadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.3,
                                            ),
                                            offset: const Offset(1, 1),
                                            blurRadius: 2,
                                          ),
                                        ],
                                        fontFamilyFallback: [
                                          AppFonts.platformLatinFont,
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 15), // 从30减少到15
                          // 底部滑块区域（使用改进的滑块组件）
                          Container(
                            margin: const EdgeInsets.only(
                              bottom: 80.0,
                              left: 24.0,
                              right: 24.0,
                            ),
                            child: _buildImprovedSlideButton(
                              AppLocalizations.of(context)!.goToCoursePage,
                              false, // 没有课程数据
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // 当前选中的课程
    final currentCourse = courses[_currentCourseIndex];

    // 课程英文名称（如果不存在，则使用默认文本）
    final String englishName =
        currentCourse['english_name'] ??
        '（测试）Learning course ${_currentCourseIndex + 1}';

    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            // 主要内容
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: Container()), // 填充顶部空间
                  // 滑块区域
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 80.0,
                    ), // 增加底部边距，为导航栏留出空间
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 课程标题
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.course,
                                style: AppFonts.createMixedStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.w900,
                                  color: Colors.white,
                                ).copyWith(
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.5,
                                      ),
                                      offset: const Offset(0, 1),
                                      blurRadius: 1,
                                    ),
                                    Shadow(
                                      color: Colors.white.withValues(
                                        alpha: 0.2,
                                      ),
                                      offset: const Offset(0, -0.5),
                                      blurRadius: 0,
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                width:
                                    MediaQuery.of(context).size.width -
                                    48, // 左右各24的padding
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    currentCourse['course_name'] ??
                                        'Course（测试）',
                                    style: AppFonts.createMixedStyle(
                                      fontWeight: FontWeight.w900,
                                      fontSize: 36,
                                      color: Colors.white,
                                    ).copyWith(
                                      shadows: [
                                        Shadow(
                                          color: Colors.black.withValues(
                                            alpha: 0.5,
                                          ),
                                          offset: const Offset(0, 1),
                                          blurRadius: 1,
                                        ),
                                        Shadow(
                                          color: Colors.white.withValues(
                                            alpha: 0.2,
                                          ),
                                          offset: const Offset(0, -0.5),
                                          blurRadius: 0,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 5), // 从10减少到5
                              SizedBox(
                                width:
                                    MediaQuery.of(context).size.width -
                                    48, // 左右各24的padding
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  alignment: Alignment.centerLeft,
                                  child: Text.rich(
                                    TextSpan(
                                      text: englishName,
                                      style: TextStyle(
                                        fontFamily: AppFonts.platformLatinFont,
                                        fontSize: 32,
                                        fontWeight: FontWeight.w900,
                                        foreground:
                                            Paint()
                                              ..shader = LinearGradient(
                                                colors: [
                                                  const Color(0xFFFF3333),
                                                  const Color(0xFF990000),
                                                ],
                                              ).createShader(
                                                const Rect.fromLTWH(
                                                  0.0,
                                                  0.0,
                                                  200.0,
                                                  70.0,
                                                ),
                                              ),
                                        shadows: [
                                          Shadow(
                                            color: Colors.black.withValues(
                                              alpha: 0.5,
                                            ),
                                            offset: const Offset(1, 1),
                                            blurRadius: 2,
                                          ),
                                          Shadow(
                                            color: Colors.white.withValues(
                                              alpha: 0.1,
                                            ),
                                            offset: const Offset(-0.5, -0.5),
                                            blurRadius: 0,
                                          ),
                                        ],
                                        fontFamilyFallback: [
                                          AppFonts.platformChineseFont,
                                        ],
                                      ),
                                    ),
                                    style: null, // 移除额外的阴影样式
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 15), // 从30减少到15
                        // 开始学习按钮（使用改进的滑块组件）
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 24.0),
                          child: _buildImprovedSlideButton(
                            AppLocalizations.of(context)!.startLearning,
                            true, // 有课程数据
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 添加顶部标题栏 - 包含标题和头像在同一行
            Positioned(
              top: 8,
              left: 0,
              right: 0,
              child: SafeArea(
                child: Column(
                  children: [
                    // 移除返回指示条
                    const SizedBox(height: 16),
                    // 标题行
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 左侧标题部分
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: const EdgeInsets.only(left: 20),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child:
                                  Platform.isIOS &&
                                          Localizations.localeOf(
                                                context,
                                              ).languageCode !=
                                              'zh'
                                      // 英文标题使用加强版超粗字体效果
                                      ? AppFonts.createBoldTextStack(
                                        AppLocalizations.of(
                                              context,
                                            )?.learningPageTitle ??
                                            "Learn",
                                        fontSize: 36,
                                        letterSpacing: -0.5, // 使用负值使字符更加紧凑
                                      )
                                      // 中文标题使用原有方法
                                      : Text(
                                        AppLocalizations.of(
                                              context,
                                            )?.learningPageTitle ??
                                            "学习",
                                        style: AppFonts.createTitleStyle(
                                          fontSize: 36,
                                          isLatinText:
                                              Localizations.localeOf(
                                                context,
                                              ).languageCode !=
                                              'zh',
                                        ),
                                      ),
                            ),
                          ),
                        ),

                        // 右侧头像按钮
                        Expanded(
                          flex: 1,
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 16),
                              child: LiquidGlassAvatarWidget(size: 36),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Apple Music 风格的横向滑动课程卡片区域
                    const SizedBox(height: 20),

                    // 横向滑动课程卡片 - 使用PageView替代ListView实现分页吸附效果
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0),
                      child: SizedBox(
                        height: 290, // 从260增加到290，容纳更大的卡片
                        width:
                            MediaQuery.of(context).size.width - 20.0, // 减去左边距
                        child:
                            courses.isEmpty
                                ? const Center(
                                  child: Text(
                                    "暂无课程",
                                    style: TextStyle(color: Colors.white54),
                                  ),
                                )
                                : PageView.builder(
                                  controller: _courseCardController,
                                  itemCount: courses.length,
                                  padEnds: false,
                                  physics: const PageScrollPhysics(),
                                  onPageChanged: (index) {
                                    // 更新当前课程索引并刷新UI
                                    setState(() {
                                      _currentCourseIndex = index;
                                    });
                                  },
                                  itemBuilder: (context, index) {
                                    final course = courses[index];
                                    final description = course['description'];
                                    final courseName =
                                        course['course_name'] ??
                                        'Python 入门实战课（测试）';
                                    final courseTag =
                                        course['tag'] ??
                                        "LEARNING PROGRESS（测试）";
                                    final courseSubtitle =
                                        course['subtitle'] ??
                                        "WakeUp Skill Boosting（测试）";

                                    // 调整卡片padding，使所有卡片位置统一
                                    return Padding(
                                      padding: const EdgeInsets.only(
                                        right: 8.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // 文字区域
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              // 使用粗体效果展示课程标签
                                              Align(
                                                alignment: Alignment.centerLeft,
                                                child:
                                                    AppFonts.createBoldTextStack(
                                                      courseTag.toUpperCase(),
                                                      fontSize: 12,
                                                      color: const Color(
                                                        0xFF8D8C92,
                                                      ),
                                                      letterSpacing: 0.0,
                                                      height: 1.2, // 设置行高为1.2倍
                                                      useCompactSpacing: false,
                                                    ),
                                              ),
                                              const SizedBox(height: 2),
                                              Text(
                                                courseName,
                                                style:
                                                    AppFonts.createMixedStyle(
                                                      fontWeight:
                                                          FontWeight.w900,
                                                      fontSize: 18,
                                                      color: Colors.white,
                                                      height: 1.2, // 设置行高为1.2倍
                                                    ).copyWith(
                                                      shadows: [
                                                        Shadow(
                                                          color: Colors.black
                                                              .withValues(
                                                                alpha: 0.5,
                                                              ),
                                                          offset: const Offset(
                                                            0,
                                                            1,
                                                          ),
                                                          blurRadius: 1,
                                                        ),
                                                        Shadow(
                                                          color: Colors.white
                                                              .withValues(
                                                                alpha: 0.2,
                                                              ),
                                                          offset: const Offset(
                                                            0,
                                                            -0.5,
                                                          ),
                                                          blurRadius: 0,
                                                        ),
                                                      ],
                                                    ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              const SizedBox(height: 2),
                                              // 第三行文本，加粗并修改颜色
                                              SizedBox(
                                                width: double.infinity,
                                                child: Align(
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child:
                                                      AppFonts.createBoldTextStack(
                                                        courseSubtitle,
                                                        fontSize: 14,
                                                        color: const Color(
                                                          0xFF8D8C92,
                                                        ),
                                                        letterSpacing: 0.0,
                                                        height:
                                                            1.2, // 设置行高为1.2倍
                                                        useCompactSpacing:
                                                            false,
                                                      ),
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 8,
                                              ), // 设置文本区域与下方卡片的间距为8pt
                                            ],
                                          ),

                                          // 课程卡片部分 - 使用新的渐变卡片组件
                                          Hero(
                                            tag:
                                                'quiz_card_learn_${course['course_id']}_$index',
                                            child: GradientCourseCard(
                                              courseName: courseName,
                                              description: description,
                                              categoryId:
                                                  course['course_id'] is int
                                                      ? course['course_id']
                                                      : int.tryParse(
                                                        course['course_id']
                                                            .toString(),
                                                      ),
                                              height: 200, // 稍微更高一些
                                              borderRadius: 18, // 圆角更小一些
                                              onTap: () {
                                                final courseId =
                                                    course['course_id'] is int
                                                        ? course['course_id']
                                                        : int.tryParse(
                                                              course['course_id']
                                                                  .toString(),
                                                            ) ??
                                                            0;
                                                if (courseId > 0) {
                                                  _navigateToAiQuizPage(
                                                    courseId,
                                                    courseName,
                                                  );
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);

    return Scaffold(
      body:
          userProvider.isLoggedIn
              ? (_isLoading
                  ? _buildLoadingView()
                  : _errorMessage != null
                  ? _buildErrorView()
                  : _userCourses == null
                  ? _buildLoadingView() // 防止未初始化的情况
                  : FutureBuilder<List<Map<String, dynamic>>>(
                    future: _userCourses,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return _buildLoadingView();
                      }
                      if (snapshot.hasError) {
                        return _buildErrorView();
                      }

                      final List<Map<String, dynamic>> data =
                          snapshot.data ?? [];
                      return _buildMainView(data);
                    },
                  ))
              : _buildNotLoggedInView(),
    );
  }
}

// 噪点纹理生成类
class NoiseTexture {
  static late ui.Image noiseTexture;

  static Future<void> initialize() async {
    // 创建一个简单的1x1透明图像，不再生成噪点
    final size = 1;
    final pixels = Uint8List(size * size * 4);
    // 设置为完全透明
    pixels[0] = 0;
    pixels[1] = 0;
    pixels[2] = 0;
    pixels[3] = 0;

    final completer = Completer<ui.Image>();
    ui.decodeImageFromPixels(
      pixels,
      size,
      size,
      ui.PixelFormat.rgba8888,
      completer.complete,
    );
    noiseTexture = await completer.future;
  }
}

// 移除噪点纹理效果，只返回子组件
class GrainOverlay extends StatelessWidget {
  final Widget child;
  final double opacity;

  const GrainOverlay({super.key, required this.child, this.opacity = 0.15});

  @override
  Widget build(BuildContext context) {
    // 直接返回子组件，不再添加噪点纹理
    return child;
  }
}

// 改进的滑块组件，避免动画控制器冲突
class ImprovedSlideButton extends StatelessWidget {
  final String text;
  final VoidCallback? onSlideComplete;
  final double progress;
  final double slideOffset;
  final Function(DragStartDetails)? onDragStart;
  final Function(DragUpdateDetails)? onDragUpdate;
  final Function(DragEndDetails)? onDragEnd;

  const ImprovedSlideButton({
    super.key,
    required this.text,
    this.onSlideComplete,
    this.progress = 0.0,
    this.slideOffset = 0.0,
    this.onDragStart,
    this.onDragUpdate,
    this.onDragEnd,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 84, // 与示例统一高度
      child: ClipRRect(
        borderRadius: BorderRadius.circular(50.0), // 与示例统一圆角
        child: Stack(
          children: [
            // 底层：轨道背景（从 xxx.dart 拿来的样式）
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(50.0),
                ),
              ),
            ),
            // 文字（采用 xxx.dart 的字体样式）
            Positioned.fill(
              child: Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.only(left: 92), // 与示例统一文本左内边距
                  child: Transform.translate(
                    offset: Offset(slideOffset * 0.6, 0), // 文字跟随滑块移动但速度较慢
                    child: Opacity(
                      opacity: (1.0 - progress).clamp(0.0, 1.0),
                      child: Text(
                        text,
                        style: AppFonts.createMixedStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // 滑块（采用 xxx.dart 的红色圆形与箭头设计）
            Positioned(
              left: 12.0 + slideOffset, // 与示例统一，滑块左侧间距 12
              top: 12.0, // 与示例统一，滑块顶侧间距 12
              child: SizedBox(
                width: 60,
                height: 60,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: const Color(0xFFAA0000),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: const Color(0xFFAA0000),
                      width: 3,
                    ),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.chevron_right,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
              ),
            ),
            // 拖拽检测器（最上层，覆盖整个按钮区域包括滑块）
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onHorizontalDragStart: onDragStart,
                onHorizontalDragUpdate: onDragUpdate,
                onHorizontalDragEnd: onDragEnd,
                child: Container(color: Colors.transparent),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 添加自定义的页面切换路由
class SlideUpPageRoute extends PageRouteBuilder {
  final Widget page;

  SlideUpPageRoute({required this.page})
    : super(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // 定义更加丝滑的曲线
          var curve = Curves.easeOutCubic;

          // 滑动动画: 从底部向上
          var slideAnimation = Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(parent: animation, curve: curve));

          // 缩放动画: 从略小到正常大小
          var scaleAnimation = Tween<double>(
            begin: 0.95,
            end: 1.0,
          ).animate(CurvedAnimation(parent: animation, curve: curve));

          // 透明度动画
          var fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: animation,
              curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
            ),
          );

          // 组合多个动画效果
          return FadeTransition(
            opacity: fadeAnimation,
            child: SlideTransition(
              position: slideAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    20 * (1 - animation.value),
                  ), // 动态调整圆角
                  child: child,
                ),
              ),
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 450),
        reverseTransitionDuration: const Duration(milliseconds: 350),
        opaque: false,
        barrierColor: Colors.black12,
        barrierDismissible: true,
      );
}
