import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/knowledge_node_model.dart';

/// 知识卡片组件
///
/// 显示知识节点的内容，支持展开/收起交互
class KnowledgeCard extends StatefulWidget {
  /// 知识节点数据
  final KnowledgeNode node;

  /// 点击展开回调
  final VoidCallback? onTap;

  /// 是否正在展开
  final bool isExpanding;

  /// 卡片宽度
  final double? width;

  /// 是否启用动画
  final bool enableAnimation;

  const KnowledgeCard({
    super.key,
    required this.node,
    this.onTap,
    this.isExpanding = false,
    this.width,
    this.enableAnimation = true,
  });

  @override
  State<KnowledgeCard> createState() => _KnowledgeCardState();
}

class _KnowledgeCardState extends State<KnowledgeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 缩放动画
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    // 透明度动画
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // 启动动画
    if (widget.enableAnimation) {
      _animationController.forward();
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.enableAnimation ? _scaleAnimation.value : 1.0,
          child: Opacity(
            opacity: widget.enableAnimation ? _opacityAnimation.value : 1.0,
            child: _buildCard(),
          ),
        );
      },
    );
  }

  /// 构建卡片主体
  Widget _buildCard() {
    return Container(
      width: widget.width,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        elevation: widget.node.isExpanded ? 8 : 4,
        borderRadius: BorderRadius.circular(16),
        shadowColor: Colors.black.withValues(alpha: 0.1),
        child: InkWell(
          onTap: widget.node.isExpanded ? null : widget.onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: _getCardGradient(),
              border: Border.all(
                color: _getBorderColor(),
                width: widget.node.isExpanded ? 2 : 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(),
                  if (widget.node.isExpanded) ...[
                    const SizedBox(height: 12),
                    _buildContent(),
                  ],
                  if (widget.isExpanding) ...[
                    const SizedBox(height: 12),
                    _buildLoadingIndicator(),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建卡片头部
  Widget _buildHeader() {
    return Row(
      children: [
        // 层级指示器
        _buildLevelIndicator(),
        const SizedBox(width: 12),

        // 标题
        Expanded(
          child: Text(
            widget.node.title,
            style: TextStyle(
              fontSize: widget.node.isExpanded ? 18 : 16,
              fontWeight:
                  widget.node.isExpanded ? FontWeight.bold : FontWeight.w600,
              color: _getTitleColor(),
              height: 1.3,
            ),
            maxLines: widget.node.isExpanded ? null : 2,
            overflow: widget.node.isExpanded ? null : TextOverflow.ellipsis,
          ),
        ),

        // 状态指示器
        _buildStatusIndicator(),
      ],
    );
  }

  /// 构建层级指示器
  Widget _buildLevelIndicator() {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _getLevelColor(),
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: Center(
        child: Text(
          '${widget.node.level}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator() {
    if (widget.isExpanding) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CupertinoActivityIndicator(radius: 8),
      );
    }

    if (widget.node.isExpanded) {
      return Icon(
        CupertinoIcons.checkmark_circle_fill,
        color: Colors.green.shade600,
        size: 20,
      );
    }

    return Icon(
      CupertinoIcons.plus_circle,
      color: Colors.blue.shade600,
      size: 20,
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200, width: 1),
      ),
      child: Text(
        widget.node.content.isEmpty ? '正在加载内容...' : widget.node.content,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade800,
          height: 1.5,
        ),
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CupertinoActivityIndicator(),
          const SizedBox(width: 12),
          Text(
            '正在探索知识分支...',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 获取卡片渐变色
  LinearGradient _getCardGradient() {
    if (widget.node.isExpanded) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.blue.shade50, Colors.indigo.shade50],
      );
    }

    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [Colors.grey.shade50, Colors.grey.shade100],
    );
  }

  /// 获取边框颜色
  Color _getBorderColor() {
    if (widget.node.isExpanded) {
      return Colors.blue.shade300;
    }

    if (widget.isExpanding) {
      return Colors.orange.shade300;
    }

    return Colors.grey.shade300;
  }

  /// 获取标题颜色
  Color _getTitleColor() {
    if (widget.node.isExpanded) {
      return Colors.blue.shade800;
    }

    return Colors.grey.shade800;
  }

  /// 获取层级颜色
  Color _getLevelColor() {
    final colors = [
      Colors.purple.shade600, // 根节点
      Colors.blue.shade600, // 第一层
      Colors.green.shade600, // 第二层
      Colors.orange.shade600, // 第三层
      Colors.red.shade600, // 第四层
      Colors.teal.shade600, // 第五层及以上
    ];

    final index = widget.node.level.clamp(0, colors.length - 1);
    return colors[index];
  }
}
