import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart';
import 'package:wakeup_app/providers/user_provider.dart';
import 'package:wakeup_app/widgets/account_bottom_sheet.dart';
import 'package:flutter/foundation.dart';

/// 液态玻璃头像组件
///
/// 使用液态玻璃效果的现代化头像组件，替代webp动画头像
/// 如果用户设置了头像，则显示用户头像
/// 否则显示带液态玻璃效果的人头图标
class LiquidGlassAvatarWidget extends StatelessWidget {
  /// 头像尺寸，默认为48.0
  final double size;

  const LiquidGlassAvatarWidget({super.key, required this.size});

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final avatarUrl = userProvider.avatarUrl;

    if (kDebugMode) {
      debugPrint(
        '🔮 构建液态玻璃头像组件: 用户登录状态=${userProvider.isLoggedIn}, 头像URL=$avatarUrl',
      );
    }

    // 如果用户已登录且设置了头像
    if (userProvider.isLoggedIn && avatarUrl != null && avatarUrl.isNotEmpty) {
      return GestureDetector(
        onTap: () => showAccountBottomSheet(context),
        child: _buildUserAvatar(avatarUrl, context),
      );
    } else {
      // 未登录或未设置头像时显示液态玻璃默认头像
      return _buildLiquidGlassAvatar(context);
    }
  }

  /// 构建用户自定义头像（头像置于液态玻璃上方）
  Widget _buildUserAvatar(String avatarUrl, BuildContext context) {
    return GestureDetector(
      onTap: () => showAccountBottomSheet(context),
      child: SizedBox(
        width: size,
        height: size,
        child: Stack(
          children: [
            // 底层：液态玻璃背景装饰
            LiquidGlass(
              shape: LiquidRoundedSuperellipse(
                borderRadius: Radius.circular(size / 2),
              ),
              child: SizedBox(
                width: size,
                height: size,
              ),
            ),
            // 上层：用户头像
            Center(
              child: ClipOval(
                child: SizedBox(
                  width: size * 0.85, // 略小于背景，留出液态玻璃边缘
                  height: size * 0.85,
                  child: Image.network(
                    avatarUrl,
                    width: size * 0.85,
                    height: size * 0.85,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      if (kDebugMode) {
                        debugPrint('❌ 用户头像加载失败: $error');
                      }
                      // 加载失败时显示默认图标
                      return _buildDefaultLiquidGlassContent();
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建液态玻璃默认头像（使用分层结构保持一致性）
  Widget _buildLiquidGlassAvatar(BuildContext context) {
    return GestureDetector(
      onTap: () => showAccountBottomSheet(context),
      child: SizedBox(
        width: size,
        height: size,
        child: Stack(
          children: [
            // 底层：液态玻璃背景装饰
            LiquidGlass(
              shape: LiquidRoundedSuperellipse(
                borderRadius: Radius.circular(size / 2),
              ),
              child: SizedBox(
                width: size,
                height: size,
              ),
            ),
            // 上层：人头图标
            Center(
              child: _buildDefaultLiquidGlassContent(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建默认的液态玻璃内容（人头图标）
  Widget _buildDefaultLiquidGlassContent() {
    return Center(
      child: Icon(
        Icons.person,
        size: size * 0.6,
        color: Colors.white.withValues(alpha: 0.9),
      ),
    );
  }
}