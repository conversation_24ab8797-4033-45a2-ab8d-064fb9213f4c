import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 连接线组件
///
/// 绘制思维导图中节点之间的扇形艺术化连接线
class ConnectionLine extends StatefulWidget {
  /// 起始点Y坐标
  final double startY;

  /// 结束点Y坐标
  final double endY;

  /// 连接线宽度
  final double width;

  /// 父节点层级
  final int parentLevel;

  /// 子节点索引
  final int childIndex;

  /// 子节点总数
  final int totalChildren;

  /// 是否启用动画
  final bool enableAnimation;

  const ConnectionLine({
    super.key,
    required this.startY,
    required this.endY,
    this.width = 200,
    this.parentLevel = 0,
    this.childIndex = 0,
    this.totalChildren = 1,
    this.enableAnimation = true,
  });

  @override
  State<ConnectionLine> createState() => _ConnectionLineState();
}

class _ConnectionLineState extends State<ConnectionLine>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: Duration(milliseconds: 800 + (widget.childIndex * 200)),
      vsync: this,
    );

    // 进度动画
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    // 启动动画
    if (widget.enableAnimation) {
      Future.delayed(Duration(milliseconds: widget.childIndex * 100), () {
        if (mounted) {
          _animationController.forward();
        }
      });
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return CustomPaint(
          size: Size(widget.width, (widget.endY - widget.startY).abs()),
          painter: ConnectionLinePainter(
            startY: widget.startY,
            endY: widget.endY,
            parentLevel: widget.parentLevel,
            childIndex: widget.childIndex,
            totalChildren: widget.totalChildren,
            progress: widget.enableAnimation ? _progressAnimation.value : 1.0,
          ),
        );
      },
    );
  }
}

/// 连接线绘制器
///
/// 负责绘制扇形艺术化的连接线
class ConnectionLinePainter extends CustomPainter {
  final double startY;
  final double endY;
  final int parentLevel;
  final int childIndex;
  final int totalChildren;
  final double progress;

  ConnectionLinePainter({
    required this.startY,
    required this.endY,
    required this.parentLevel,
    required this.childIndex,
    required this.totalChildren,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (progress <= 0) return;

    // 计算连接线的路径
    final path = _createConnectionPath(size);

    // 创建渐变画笔
    final paint = _createGradientPaint(size);

    // 绘制连接线
    canvas.drawPath(path, paint);

    // 绘制装饰点
    _drawDecorationDots(canvas, size);
  }

  /// 创建连接线路径
  Path _createConnectionPath(Size size) {
    final path = Path();

    // 起始点和结束点
    final startPoint = Offset(0, 0);
    final endPoint = Offset(size.width, size.height);

    // 计算控制点
    final controlPoint1 = _calculateControlPoint1(size);
    final controlPoint2 = _calculateControlPoint2(size);

    // 创建贝塞尔曲线路径
    path.moveTo(startPoint.dx, startPoint.dy);

    if (progress < 1.0) {
      // 动画进行中，只绘制部分路径
      final animatedEndPoint = Offset(
        startPoint.dx + (endPoint.dx - startPoint.dx) * progress,
        startPoint.dy + (endPoint.dy - startPoint.dy) * progress,
      );

      final animatedControl1 = Offset(
        startPoint.dx + (controlPoint1.dx - startPoint.dx) * progress,
        startPoint.dy + (controlPoint1.dy - startPoint.dy) * progress,
      );

      final animatedControl2 = Offset(
        startPoint.dx + (controlPoint2.dx - startPoint.dx) * progress,
        startPoint.dy + (controlPoint2.dy - startPoint.dy) * progress,
      );

      path.cubicTo(
        animatedControl1.dx,
        animatedControl1.dy,
        animatedControl2.dx,
        animatedControl2.dy,
        animatedEndPoint.dx,
        animatedEndPoint.dy,
      );
    } else {
      // 完整路径
      path.cubicTo(
        controlPoint1.dx,
        controlPoint1.dy,
        controlPoint2.dx,
        controlPoint2.dy,
        endPoint.dx,
        endPoint.dy,
      );
    }

    return path;
  }

  /// 计算第一个控制点
  Offset _calculateControlPoint1(Size size) {
    final fanAngle = _calculateFanAngle();
    final controlDistance = size.width * 0.4;

    return Offset(
      controlDistance * math.cos(fanAngle),
      controlDistance * math.sin(fanAngle),
    );
  }

  /// 计算第二个控制点
  Offset _calculateControlPoint2(Size size) {
    final fanAngle = _calculateFanAngle();
    final controlDistance = size.width * 0.6;

    return Offset(
      size.width - controlDistance * math.cos(fanAngle),
      size.height - controlDistance * math.sin(fanAngle),
    );
  }

  /// 计算扇形角度
  double _calculateFanAngle() {
    if (totalChildren <= 1) return 0;

    // 扇形总角度（弧度）
    final totalAngle = math.pi / 3; // 60度

    // 计算当前子节点的角度偏移
    final angleStep = totalAngle / (totalChildren - 1);
    final currentAngle = -totalAngle / 2 + (childIndex * angleStep);

    return currentAngle;
  }

  /// 创建渐变画笔
  Paint _createGradientPaint(Size size) {
    final colors = _getGradientColors();

    final gradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: colors,
      stops: const [0.0, 0.5, 1.0],
    );

    return Paint()
      ..shader = gradient.createShader(
        Rect.fromLTWH(0, 0, size.width, size.height),
      )
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0
      ..strokeCap = StrokeCap.round;
  }

  /// 获取渐变颜色
  List<Color> _getGradientColors() {
    final levelColors = [
      [Colors.purple.shade300, Colors.purple.shade500, Colors.purple.shade700],
      [Colors.blue.shade300, Colors.blue.shade500, Colors.blue.shade700],
      [Colors.green.shade300, Colors.green.shade500, Colors.green.shade700],
      [Colors.orange.shade300, Colors.orange.shade500, Colors.orange.shade700],
      [Colors.red.shade300, Colors.red.shade500, Colors.red.shade700],
      [Colors.teal.shade300, Colors.teal.shade500, Colors.teal.shade700],
    ];

    final colorIndex = parentLevel.clamp(0, levelColors.length - 1);
    return levelColors[colorIndex];
  }

  /// 绘制装饰点
  void _drawDecorationDots(Canvas canvas, Size size) {
    if (progress < 0.8) return;

    final dotPaint =
        Paint()
          ..color = _getGradientColors()[1].withValues(alpha: 0.6)
          ..style = PaintingStyle.fill;

    // 在连接线上绘制几个小装饰点
    final dotPositions = [0.3, 0.5, 0.7];

    for (final position in dotPositions) {
      final dotOffset = Offset(
        size.width * position,
        size.height * position + math.sin(position * math.pi * 2) * 10,
      );

      canvas.drawCircle(dotOffset, 2.0, dotPaint);
    }
  }

  @override
  bool shouldRepaint(ConnectionLinePainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.startY != startY ||
        oldDelegate.endY != endY ||
        oldDelegate.childIndex != childIndex ||
        oldDelegate.totalChildren != totalChildren;
  }
}
