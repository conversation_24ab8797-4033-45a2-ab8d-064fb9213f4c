import 'package:flutter/widgets.dart';

/// 在 Tab 进入时为其子树下发一次性动画触发信号
class AvatarAnimationScope extends InheritedWidget {
  final int playToken; // 递增即触发一次动画

  const AvatarAnimationScope({
    super.key,
    required this.playToken,
    required super.child,
  });

  static AvatarAnimationScope? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<AvatarAnimationScope>();
  }

  @override
  bool updateShouldNotify(covariant AvatarAnimationScope oldWidget) {
    // 当 token 变化时，子树重建，从而 AnimatedAvatarWidget 能检测到变化
    return oldWidget.playToken != playToken;
  }
}
