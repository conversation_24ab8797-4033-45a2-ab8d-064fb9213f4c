import 'package:flutter/material.dart';

/// 自定义三个点菜单按钮
class CustomMenuButton extends StatefulWidget {
  /// 按钮尺寸
  final double size;

  /// 背景颜色
  final Color backgroundColor;

  /// 点的颜色
  final Color dotColor;

  /// 按钮点击回调
  final VoidCallback onPressed;

  const CustomMenuButton({
    super.key,
    this.size = 26.0, // 进一步减小默认尺寸
    this.backgroundColor = Colors.black26, // 使用更透明的黑色
    this.dotColor = Colors.white, // 保持白色点
    required this.onPressed,
  });

  @override
  State<CustomMenuButton> createState() => _CustomMenuButtonState();
}

class _CustomMenuButtonState extends State<CustomMenuButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isPressed = false; // ignore: unused_field

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100), // 减少动画时间，更快响应
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onPressed,
      onTapDown: (_) {
        setState(() {
          _isPressed = true;
        });
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
      },
      onTapCancel: () {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final scale = 1.0 - (_animationController.value * 0.05);
          return Transform.scale(
            scale: scale,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                shape: BoxShape.circle,
                // 添加轻微的内阴影效果，使按钮看起来更轻量
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 2,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Center(child: _buildMenuDots()),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMenuDots() {
    // 水平排列的三个点
    final double dotSize = 2.5; // 进一步减小点的大小
    final double dotSpacing = 2.5; // 进一步减小点之间的间距

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildDot(dotSize),
        SizedBox(width: dotSpacing),
        _buildDot(dotSize),
        SizedBox(width: dotSpacing),
        _buildDot(dotSize),
      ],
    );
  }

  Widget _buildDot(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(color: widget.dotColor, shape: BoxShape.circle),
    );
  }
}
