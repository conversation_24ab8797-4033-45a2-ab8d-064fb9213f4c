import 'package:flutter/material.dart';

/// 筛选按钮
class FilterButton extends StatefulWidget {
  /// 按钮尺寸
  final double size;

  /// 背景颜色
  final Color backgroundColor;

  /// 线条颜色
  final Color lineColor;

  /// 是否激活筛选条件
  final bool hasActiveFilters;

  /// 按钮点击回调
  final VoidCallback onPressed;

  const FilterButton({
    super.key,
    this.size = 30.0, // 减小默认尺寸
    this.backgroundColor = Colors.black38, // 使用预定义的半透明黑色
    this.lineColor = Colors.white, // 修改为白色
    this.hasActiveFilters = false,
    required this.onPressed,
  });

  @override
  State<FilterButton> createState() => _FilterButtonState();
}

class _FilterButtonState extends State<FilterButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isPressed = false; // ignore: unused_field

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onPressed,
      onTapDown: (_) {
        setState(() {
          _isPressed = true;
        });
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
      },
      onTapCancel: () {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final scale = 1.0 - (_animationController.value * 0.05);
          return Transform.scale(
            scale: scale,
            child: Stack(
              children: [
                Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    color: widget.backgroundColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(child: _buildFilterLines()),
                ),
                if (widget.hasActiveFilters)
                  Positioned(
                    right: 2,
                    top: 2,
                    child: Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: widget.lineColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFilterLines() {
    // 计算线条的大小比例
    final double lineHeight = 2.0; // 减小线条高度
    final double lineSpacing = 2.0; // 减小线条间距
    final double topLineWidth = widget.size * 0.5; // 最长线条
    final double middleLineWidth = widget.size * 0.4; // 中等线条
    final double bottomLineWidth = widget.size * 0.3; // 最短线条

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLine(topLineWidth, lineHeight),
        SizedBox(height: lineSpacing),
        _buildLine(middleLineWidth, lineHeight),
        SizedBox(height: lineSpacing),
        _buildLine(bottomLineWidth, lineHeight),
      ],
    );
  }

  Widget _buildLine(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: widget.lineColor,
        borderRadius: BorderRadius.circular(height / 2), // 圆角效果
      ),
    );
  }
}
