import 'dart:ui';
import 'package:flutter/material.dart';

/// 答题页面的骨架屏加载组件
///
/// 在真实内容加载完成前显示，提供与答题页面相似的布局和Cupertino风格的加载动画
class SkeletonQuizPage extends StatefulWidget {
  const SkeletonQuizPage({super.key});

  @override
  State<SkeletonQuizPage> createState() => _SkeletonQuizPageState();
}

class _SkeletonQuizPageState extends State<SkeletonQuizPage>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;

  // 添加自动进度控制器
  late AnimationController _progressController;

  // 模拟加载进度
  double _loadingProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController.unbounded(vsync: this)
      ..repeat(min: -0.5, max: 1.5, period: const Duration(milliseconds: 1000));

    // 自动递增进度，提供用户反馈
    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..addListener(() {
      if (mounted) {
        setState(() {
          // 模拟80%的最大加载进度，留下20%给真实加载
          _loadingProgress = _progressController.value * 0.8;
        });
      }
    });

    _progressController.forward();

    // 自动启动轻微的微动画
    _startMicroAnimations();
  }

  // 微动画，使骨架屏看起来更"活跃"
  void _startMicroAnimations() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          // 触发微小的重建，产生微动画效果
        });
        _startMicroAnimations(); // 递归调用，持续产生微动画
      }
    });
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.8),
              Colors.black.withValues(alpha: 0.9),
            ],
          ),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: SafeArea(
            child: Column(
              children: [
                _buildNavigationBar(),
                _buildProgressIndicator(),
                Expanded(
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildQuestionSkeleton(),
                          const SizedBox(height: 24),
                          _buildOptionsSkeleton(),
                          const SizedBox(height: 32),
                          _buildExplanationSkeleton(),
                        ],
                      ),
                    ),
                  ),
                ),
                _buildBottomActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 添加加载进度指示器
  Widget _buildProgressIndicator() {
    return Padding(
      padding: const EdgeInsets.only(top: 4.0, bottom: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 2,
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(1),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: _loadingProgress,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withValues(alpha: 0.5),
                        Colors.white.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 导航栏骨架
  Widget _buildNavigationBar() {
    return Container(
      height: 44,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildShimmerBox(width: 40, height: 40, borderRadius: 20),
          _buildShimmerBox(width: 120, height: 22),
          _buildShimmerBox(width: 40, height: 40, borderRadius: 20),
        ],
      ),
    );
  }

  // 问题题干骨架
  Widget _buildQuestionSkeleton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildShimmerBox(width: 80, height: 20),
        const SizedBox(height: 8),
        _buildShimmerBox(width: double.infinity, height: 100),
      ],
    );
  }

  // 选项区域骨架
  Widget _buildOptionsSkeleton() {
    return Column(
      children: List.generate(
        4,
        (index) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.grey.withValues(alpha: 0.3), width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  _buildShimmerBox(width: 30, height: 30, borderRadius: 15),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildShimmerBox(width: double.infinity, height: 20),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 解释区域骨架
  Widget _buildExplanationSkeleton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildShimmerBox(width: 100, height: 24),
        const SizedBox(height: 16),
        _buildShimmerBox(width: double.infinity, height: 60),
        const SizedBox(height: 12),
        _buildShimmerBox(width: double.infinity, height: 60),
      ],
    );
  }

  // 底部操作区骨架
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        border: Border(
          top: BorderSide(color: Colors.grey.withValues(alpha: 0.2), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildShimmerBox(width: 40, height: 40, borderRadius: 20),
          _buildShimmerBox(width: 120, height: 44, borderRadius: 22),
          _buildShimmerBox(width: 40, height: 40, borderRadius: 20),
        ],
      ),
    );
  }

  // 创建带闪烁效果的占位框
  Widget _buildShimmerBox({
    required double width,
    required double height,
    double borderRadius = 8,
  }) {
    return AnimatedBuilder(
      animation: _shimmerController,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            gradient: LinearGradient(
              colors: [
                Colors.grey.shade800.withValues(alpha: 0.5),
                Colors.grey.shade600.withValues(alpha: 0.5),
                Colors.grey.shade800.withValues(alpha: 0.5),
              ],
              stops: const [0.0, 0.5, 1.0],
              begin: Alignment(-1.0 + _shimmerController.value * 2.0, 0),
              end: Alignment(1.0 + _shimmerController.value * 2.0, 0),
            ),
          ),
        );
      },
    );
  }
}

/// 滑动渐变转换器 - 用于创建滑动的闪光效果
class _SlidingGradientTransform extends GradientTransform { // ignore: unused_element
  const _SlidingGradientTransform({required this.slidePercent});

  final double slidePercent;

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    return Matrix4.translationValues(bounds.width * slidePercent, 0.0, 0.0);
  }
}
