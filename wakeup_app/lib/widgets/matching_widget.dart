import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../services/auth_service.dart';
import '../core/utils/logger.dart';

/// 匹配题组件
class MatchingWidget extends StatefulWidget {
  final Question question;
  final Map<String, String>? userAnswers;
  final Function(Map<String, String>) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const MatchingWidget({
    super.key,
    required this.question,
    this.userAnswers,
    required this.onAnswerChanged,
    required this.interactionState,
  });

  @override
  State<MatchingWidget> createState() => _MatchingWidgetState();
}

class _MatchingWidgetState extends State<MatchingWidget> {
  late Map<String, String> _userMatches;
  late List<String> _availableRightItems;
  late List<MatchingItem> _matchingItems;
  String? _selectedLeftItem;
  String? _lastSelectedItem;
  List<Map<String, dynamic>> _smartSuggestions = [];
  bool _isLoadingSuggestions = false;
  bool _showSuggestions = false;
  String? _highlightedSuggestion;

  @override
  void initState() {
    super.initState();
    _initializeMatchingData();
    _loadSmartSuggestions();
  }

  @override
  void didUpdateWidget(MatchingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userAnswers != widget.userAnswers) {
      _initializeMatchingData(keepSelection: true);
    }

    // 重新应用最后选中的项目
    if (_lastSelectedItem != null) {
      setState(() {
        _selectedLeftItem = _lastSelectedItem;
      });
    }
  }

  @override
  void dispose() {
    _lastSelectedItem = null;
    super.dispose();
  }

  void _initializeMatchingData({bool keepSelection = false}) {
    // 初始化匹配项
    _matchingItems = widget.question.matchingItems ?? [];

    // 在初始化前先保存选中状态
    if (_selectedLeftItem != null) {
      _lastSelectedItem = _selectedLeftItem;
    }

    // 初始化用户匹配
    _userMatches = Map<String, String>.from(widget.userAnswers ?? {});

    // 获取所有右侧选项
    List<String> allRightItems =
        _matchingItems.map((item) => item.right).toList();

    // 获取剩余可用的右侧选项（尚未被匹配的）
    _availableRightItems = List<String>.from(allRightItems);
    for (final rightItem in _userMatches.values) {
      _availableRightItems.remove(rightItem);
    }

    // 只有在不需要保持选择状态时才清除
    if (!keepSelection) {
      _selectedLeftItem = null;
      _lastSelectedItem = null;
    } else if (_lastSelectedItem != null) {
      _selectedLeftItem = _lastSelectedItem;
    }
  }

  /// 加载智能匹配建议
  Future<void> _loadSmartSuggestions() async {
    if (widget.interactionState !=
        QuestionInteractionState.waitingForSelection) {
      return;
    }

    setState(() {
      _isLoadingSuggestions = true;
    });

    try {
      final response = await http.get(
        Uri.parse(
          '${AuthService.baseUrl}/api/smart_hints/matching/${widget.question.id}',
        ),
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          setState(() {
            _smartSuggestions = List<Map<String, dynamic>>.from(
              data['suggestions'] ?? [],
            );
            _showSuggestions = _smartSuggestions.isNotEmpty;
          });
        }
      } else {
        _setDefaultSuggestions();
      }
    } catch (e) {
      AppLogger.error('加载匹配题智能建议失败', error: e);
      _setDefaultSuggestions();
    } finally {
      setState(() {
        _isLoadingSuggestions = false;
      });
    }
  }

  /// 设置默认智能建议
  void _setDefaultSuggestions() {
    // 基于内容分析生成简单的匹配建议
    List<Map<String, dynamic>> suggestions = [];

    for (var item in _matchingItems) {
      // 计算相似度建议（简化版）
      for (var otherItem in _matchingItems) {
        if (item != otherItem) {
          final leftWords = item.left.toLowerCase().split(' ');
          final rightWords = otherItem.right.toLowerCase().split(' ');

          // 简单的词汇匹配度计算
          int matchCount = 0;
          for (var word in leftWords) {
            if (rightWords.any((w) => w.contains(word) || word.contains(w))) {
              matchCount++;
            }
          }

          if (matchCount > 0) {
            suggestions.add({
              'left': item.left,
              'right': otherItem.right,
              'confidence': (matchCount / leftWords.length * 100).round(),
              'reason': '词汇相似度匹配',
            });
          }
        }
      }
    }

    // 按置信度排序
    suggestions.sort((a, b) => b['confidence'].compareTo(a['confidence']));

    setState(() {
      _smartSuggestions = suggestions.take(5).toList(); // 最多显示5个建议
      _showSuggestions = true;
    });
  }

  /// 应用智能建议
  void _applySuggestion(Map<String, dynamic> suggestion) {
    final leftItem = suggestion['left'] as String;
    final rightItem = suggestion['right'] as String;

    // 检查左侧项目是否已被匹配
    if (_userMatches.containsKey(leftItem)) {
      // 如果已匹配，先释放原来的右侧选项
      _availableRightItems.add(_userMatches[leftItem]!);
      _userMatches.remove(leftItem);
    }

    // 应用新的匹配
    setState(() {
      _userMatches[leftItem] = rightItem;
      _availableRightItems.remove(rightItem);
      _highlightedSuggestion = '$leftItem -> $rightItem';

      widget.onAnswerChanged(_userMatches);
    });

    // 添加触觉反馈
    HapticFeedback.mediumImpact();

    // 3秒后清除高亮
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _highlightedSuggestion = null;
        });
      }
    });
  }

  /// 自动匹配所有明显的配对
  void _autoMatchObvious() {
    int matchedCount = 0;

    for (var suggestion in _smartSuggestions) {
      if (suggestion['confidence'] >= 80) {
        // 高置信度的自动匹配
        final leftItem = suggestion['left'] as String;
        final rightItem = suggestion['right'] as String;

        if (!_userMatches.containsKey(leftItem) &&
            _availableRightItems.contains(rightItem)) {
          _userMatches[leftItem] = rightItem;
          _availableRightItems.remove(rightItem);
          matchedCount++;
        }
      }
    }

    if (matchedCount > 0) {
      setState(() {
        widget.onAnswerChanged(_userMatches);
      });
      HapticFeedback.heavyImpact();
    }
  }

  // 保留备用：当启用下拉选择时用于更新左侧已选项
  // ignore: unused_element
  void _updateSelectedItem(String? newSelectedItem) {
    setState(() {
      _selectedLeftItem = newSelectedItem;
      if (newSelectedItem != null) {
        _lastSelectedItem = newSelectedItem;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;

    return Column(
      children: [
        // 指导文字和智能操作按钮
        Row(
          children: [
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(bottom: 16),
                child: Text(
                  "请将左侧项目与右侧选项匹配：",
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Noto Sans SC',
                  ),
                ),
              ),
            ),
            if (!isShowingFeedback) ...[
              // 智能建议按钮
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showSuggestions = !_showSuggestions;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color:
                        _showSuggestions
                            ? Colors.blue.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '智能建议',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // 自动匹配按钮
              GestureDetector(
                onTap: _autoMatchObvious,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.5),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.auto_fix_high, color: Colors.green, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '自动匹配',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),

        // 匹配区域 - 使用左右布局
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 左右布局
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 左侧列表
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: Text(
                            "待匹配项",
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        ...List.generate(_matchingItems.length, (index) {
                          final item = _matchingItems[index];
                          final isSelected = _selectedLeftItem == item.left;
                          final hasMatch = _userMatches.containsKey(item.left);
                          final matchedRight = _userMatches[item.left] ?? '';

                          // 评判是否匹配正确
                          bool isCorrect = false;
                          if (isShowingFeedback && hasMatch) {
                            // 找到对应的正确匹配项
                            final correctItem = _matchingItems.firstWhere(
                              (element) => element.left == item.left,
                              orElse:
                                  () =>
                                      MatchingItem(id: -1, left: '', right: ''),
                            );
                            isCorrect = correctItem.right == matchedRight;
                          }

                          return GestureDetector(
                            onTap:
                                isShowingFeedback
                                    ? null
                                    : () {
                                      setState(() {
                                        // 如果已经匹配，则取消匹配
                                        if (hasMatch) {
                                          _availableRightItems.add(
                                            _userMatches[item.left]!,
                                          );
                                          _userMatches.remove(item.left);
                                          _selectedLeftItem = null;
                                          _lastSelectedItem = null;
                                        } else {
                                          // 选择新项目或取消选择
                                          String? newSelection =
                                              _selectedLeftItem == item.left
                                                  ? null
                                                  : item.left;
                                          _selectedLeftItem = newSelection;
                                          _lastSelectedItem = newSelection;
                                        }
                                      });
                                    },
                            child: Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              padding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    isSelected
                                        ? Colors.purple.withValues(alpha: 0.3)
                                        : Colors.black.withValues(alpha: 0.5),
                                borderRadius: BorderRadius.circular(24),
                                border: Border.all(
                                  color:
                                      isSelected
                                          ? Colors.purple.shade300
                                          : isShowingFeedback && hasMatch
                                          ? isCorrect
                                              ? Colors.green
                                              : Colors.red
                                          : Colors.white.withValues(alpha: 0.2),
                                  width: 2,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      item.left,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontFamily: 'Noto Sans SC',
                                      ),
                                    ),
                                  ),
                                  if (hasMatch)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            isShowingFeedback
                                                ? isCorrect
                                                    ? Colors.green.withValues(
                                                      alpha: 0.2,
                                                    )
                                                    : Colors.red.withValues(
                                                      alpha: 0.2,
                                                    )
                                                : Colors.purple.withValues(
                                                  alpha: 0.2,
                                                ),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color:
                                              isShowingFeedback
                                                  ? isCorrect
                                                      ? Colors.green
                                                      : Colors.red
                                                  : Colors.purple.shade200,
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            matchedRight,
                                            style: TextStyle(
                                              color:
                                                  isShowingFeedback
                                                      ? isCorrect
                                                          ? Colors.green
                                                          : Colors.red
                                                      : Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14,
                                              fontFamily: 'Noto Sans SC',
                                            ),
                                          ),
                                          if (!isShowingFeedback)
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                left: 6,
                                              ),
                                              child: Icon(
                                                CupertinoIcons.xmark_circle,
                                                color: Colors.white.withValues(
                                                  alpha: 0.8,
                                                ),
                                                size: 16,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  if (isShowingFeedback && !hasMatch)
                                    Padding(
                                      padding: const EdgeInsets.only(left: 8),
                                      child: Icon(
                                        CupertinoIcons.xmark_circle_fill,
                                        color: Colors.red,
                                        size: 18,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),

                  // 中间分隔线
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    width: 1,
                    height: _matchingItems.length > 3 ? 350 : 200,
                    color: Colors.white.withValues(alpha: 0.3),
                  ),

                  // 右侧选项列表
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: Text(
                            "可选答案",
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (!isShowingFeedback)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children:
                                _availableRightItems.map((rightItem) {
                                  return GestureDetector(
                                    onTap:
                                        _selectedLeftItem != null
                                            ? () {
                                              // 保存当前选中项，确保不会被清除
                                              final currentSelected =
                                                  _selectedLeftItem;

                                              setState(() {
                                                // 记录匹配关系
                                                _userMatches[currentSelected!] =
                                                    rightItem;

                                                // 从可用选项中移除
                                                _availableRightItems.remove(
                                                  rightItem,
                                                );

                                                // 通知父组件
                                                widget.onAnswerChanged(
                                                  _userMatches,
                                                );

                                                // 关键修复：确保选中状态在匹配完成后不会被清除
                                                _lastSelectedItem =
                                                    currentSelected;
                                                // 保持选中状态
                                                _selectedLeftItem =
                                                    currentSelected;
                                              });
                                            }
                                            : null,
                                    child: Container(
                                      width: double.infinity,
                                      margin: const EdgeInsets.only(bottom: 8),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 12,
                                        horizontal: 16,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            _selectedLeftItem != null
                                                ? Colors.purple.withValues(
                                                  alpha: 0.3,
                                                )
                                                : Colors.black.withValues(
                                                  alpha: 0.5,
                                                ),
                                        borderRadius: BorderRadius.circular(24),
                                        border: Border.all(
                                          color:
                                              _selectedLeftItem != null
                                                  ? Colors.purple.shade300
                                                  : Colors.white.withValues(
                                                    alpha: 0.2,
                                                  ),
                                          width: 2,
                                        ),
                                      ),
                                      child: Text(
                                        rightItem,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontFamily: 'Noto Sans SC',
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // 智能建议区域
        if (_showSuggestions &&
            !isShowingFeedback &&
            _smartSuggestions.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      color: Colors.amber,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '智能匹配建议',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () => setState(() => _showSuggestions = false),
                      child: Icon(
                        Icons.close,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Column(
                  children:
                      _smartSuggestions.take(3).map((suggestion) {
                        final isHighlighted =
                            _highlightedSuggestion ==
                            '${suggestion['left']} -> ${suggestion['right']}';
                        final confidence = suggestion['confidence'] as int;

                        return GestureDetector(
                          onTap: () => _applySuggestion(suggestion),
                          child: Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient:
                                  isHighlighted
                                      ? LinearGradient(
                                        colors: [
                                          Colors.green.withValues(alpha: 0.4),
                                          Colors.blue.withValues(alpha: 0.4),
                                        ],
                                      )
                                      : LinearGradient(
                                        colors: [
                                          Colors.blue.withValues(alpha: 0.2),
                                          Colors.purple.withValues(alpha: 0.2),
                                        ],
                                      ),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color:
                                    isHighlighted
                                        ? Colors.green
                                        : Colors.white.withValues(alpha: 0.3),
                                width: isHighlighted ? 2 : 1,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        '${suggestion['left']} ↔ ${suggestion['right']}',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 6,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            confidence >= 80
                                                ? Colors.green.withValues(
                                                  alpha: 0.3,
                                                )
                                                : confidence >= 60
                                                ? Colors.yellow.withValues(
                                                  alpha: 0.3,
                                                )
                                                : Colors.orange.withValues(
                                                  alpha: 0.3,
                                                ),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        '$confidence%',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 11,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  suggestion['reason'] ?? '智能分析建议',
                                  style: TextStyle(
                                    color: Colors.white.withValues(alpha: 0.7),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                ),
              ],
            ),
          ),
        ],

        // 加载状态
        if (_isLoadingSuggestions)
          Container(
            margin: const EdgeInsets.only(top: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '正在生成智能匹配建议...',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
