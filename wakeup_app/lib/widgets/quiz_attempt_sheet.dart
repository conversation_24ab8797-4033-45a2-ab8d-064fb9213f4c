import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
// import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import '../providers/user_provider.dart';
import '../services/quiz_service.dart';
// import '../services/quiz_attempt_service.dart';
import '../services/preload_service.dart';
import '../models/quiz_model.dart';

import '../widgets/shimmer_loading.dart';
import 'package:provider/provider.dart';

/// 题目交互阶段的枚举状态机
enum QuestionStage {
  /// 展示题目内容阶段
  displayingQuestion,

  /// 等待用户答题阶段
  waitingForAnswer,

  /// 展示解析阶段
  displayingExplanation,

  /// 等待用户可能的提问阶段
  waitingForQuestion,

  /// 展示AI回答用户提问阶段
  displayingAiResponse,

  /// 准备进入下一题阶段
  preparingNextQuestion,

  /// 展示总结过渡语阶段
  summarizing,
}

/// 聊天气泡的类型
enum BubbleType {
  ai, // AI的消息
  user, // 用户的消息
  question, // 题目
  answer, // 用户的回答
  explanation, // 题目解析
  aiResponse, // AI对用户提问的回答
}

/// 聊天气泡的模型
class ChatBubble {
  final String message;
  final BubbleType type;
  final DateTime timestamp;
  final bool isTyping; // 是否显示打字动画

  ChatBubble({
    required this.message,
    required this.type,
    DateTime? timestamp,
    this.isTyping = false,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// 题目模型类
class QuizPageQuestion {
  final int id;
  final String content;
  final String? answer;
  final String? explanation;

  QuizPageQuestion({
    required this.id,
    required this.content,
    this.answer,
    this.explanation,
  });
}

/// 分类标签模型
class QuizPageCategoryTab {
  final int id;
  final String name;
  final String? description;
  final int level4Id;
  final bool isActive;
  final String tabType;
  final String? englishName;

  QuizPageCategoryTab({
    required this.id,
    required this.name,
    this.description,
    required this.level4Id,
    required this.isActive,
    required this.tabType,
    this.englishName,
  });
}

/// 题库集合模型
class QuizPageQuestionSet {
  final int id;
  final String name;
  final String? description;
  final int categoryTabId;

  QuizPageQuestionSet({
    required this.id,
    required this.name,
    this.description,
    required this.categoryTabId,
  });
}

/// 底部弹出式答题卡
class QuizAttemptSheet extends StatefulWidget {
  final int courseId;
  final String courseName;
  final bool preloadedData;

  const QuizAttemptSheet({
    super.key,
    required this.courseId,
    required this.courseName,
    this.preloadedData = false,
  });

  @override
  State<QuizAttemptSheet> createState() => _QuizAttemptSheetState();
}

class _QuizAttemptSheetState extends State<QuizAttemptSheet>
    with SingleTickerProviderStateMixin {
  // 当前问题
  QuizPageQuestion? _currentQuestion;

  // 当前阶段
  QuestionStage _currentStage = QuestionStage.displayingQuestion;

  // 聊天气泡列表
  final List<ChatBubble> _chatBubbles = [];

  // 用户答案
  String _userAnswer = '';

  // 是否答对
  bool? _isCorrect; // ignore: unused_field

  // 用于存储获取到的题目列表和当前题目索引
  List<QuizPageQuestion> _questionList = [];
  int _currentQuestionIndex = 0;

  // 控制器
  final TextEditingController _answerController = TextEditingController();
  final TextEditingController _questionController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // 定时器，用于模拟AI打字效果
  Timer? _typingTimer;

  // 当前正在打字的气泡索引
  int? _currentTypingBubbleIndex;

  // 侧边栏和标签相关的状态变量
  bool _isLoading = false;
  String? _errorMessage;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // 标签页数据
  List<QuizPageCategoryTab> _categoryTabs = [];
  QuizPageCategoryTab? _selectedTab;

  // 题库数据
  List<QuizPageQuestionSet> _questionSets = [];
  QuizPageQuestionSet? _selectedQuestionSet;

  // 页面拖拽控制器
  late AnimationController _dragController;

  // 预加载服务实例
  final PreloadService _preloadService = PreloadService();

  @override
  void initState() {
    super.initState();

    // 初始化拖拽控制器
    _dragController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // 如果有预加载数据，使用预加载的数据
    if (widget.preloadedData) {
      _loadPreloadedData();
    } else {
      _loadCategoryTabs();
    }

    // 隐藏底部导航栏
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [SystemUiOverlay.top],
      );
    });
  }

  @override
  void dispose() {
    _answerController.dispose();
    _questionController.dispose();
    _scrollController.dispose();
    _typingTimer?.cancel();
    _dragController.dispose();

    // 恢复底部导航栏
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
    );

    super.dispose();
  }

  /// 加载分类标签
  Future<void> _loadCategoryTabs() async {
    debugPrint("开始加载分类标签");
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn) {
      setState(() {
        _errorMessage = "请先登录";
        _isLoading = false;
      });
      return;
    }

    try {
      // 从后端获取标签页数据
      final modelTabs = await QuizService.getCategoryTabs(
        widget.courseId,
        userProvider.token!,
      );

      // 将模型的CategoryTab转换为QuizPageCategoryTab
      final List<QuizPageCategoryTab> loadedTabs =
          modelTabs
              .map(
                (tab) => QuizPageCategoryTab(
                  id: tab.id,
                  name: tab.name,
                  description: tab.description,
                  level4Id: tab.level4Id,
                  isActive: tab.isActive,
                  tabType: tab.tabType,
                  englishName: tab.englishName,
                ),
              )
              .toList();

      if (mounted) {
        setState(() {
          _categoryTabs = loadedTabs;
          debugPrint("加载到标签页数量: ${_categoryTabs.length}");

          // 如果有标签页，选择第一个并加载内容
          if (_categoryTabs.isNotEmpty) {
            // 默认选择quiz类型标签，如果没有则选第一个
            _selectedTab = _categoryTabs.firstWhere(
              (tab) => tab.tabType == 'quiz',
              orElse: () => _categoryTabs.first,
            );
            _loadTabContent(_selectedTab!);
          } else {
            // 没有标签页时，直接获取题目
            _fetchNextQuestion();
          }
        });
      }
    } catch (e) {
      debugPrint("加载标签页失败: $e");
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
          // 出错时直接获取题目
          _fetchNextQuestion();
        });
      }
    }
  }

  /// 加载标签页内容
  Future<void> _loadTabContent(QuizPageCategoryTab tab) async {
    debugPrint("开始加载标签页内容: ${tab.name}, 类型: ${tab.tabType}, ID: ${tab.id}");

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn) {
      setState(() {
        _errorMessage = "请先登录";
        _isLoading = false;
      });
      return;
    }

    try {
      // 根据标签类型加载不同内容
      if (tab.tabType == 'quiz') {
        debugPrint("加载答题标签内容");
        // 加载题库
        final content = await QuizService.getLevel6Content(
          tab.id,
          userProvider.token!,
          contentType: 'quiz',
        );

        if (mounted) {
          setState(() {
            _questionSets = content.cast<QuizPageQuestionSet>();
            _selectedTab = tab;
            debugPrint("加载到${_questionSets.length}个题库");

            // 如果有题库，选择第一个
            if (_questionSets.isNotEmpty) {
              _selectedQuestionSet = _questionSets.first;
              _fetchNextQuestion();
            }
            _isLoading = false;
          });
        }
      } else {
        // 其他类型标签的处理...
        setState(() {
          _selectedTab = tab;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint("加载标签内容失败: $e");
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
          _fetchNextQuestion();
        });
      }
    }
  }

  /// 获取下一个问题
  Future<void> _fetchNextQuestion() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn) {
        setState(() {
          _errorMessage = "请先登录";
          _isLoading = false;
        });
        return;
      }

      // 如果没有加载到题目列表或者已经答完所有题目，则重新加载题目
      if (_questionList.isEmpty ||
          _currentQuestionIndex >= _questionList.length) {
        debugPrint("获取新的题目列表");
        int questionSetId = _selectedQuestionSet?.id ?? 0; // ignore: unused_local_variable

        // 获取模型的 Question 列表
        List<Question> modelQuestions = await QuizService.getQuestions(
          widget.courseId,
          userProvider.token!,
        );

        // 转换为 QuizPageQuestion 列表
        _questionList =
            modelQuestions
                .map(
                  (q) => QuizPageQuestion(
                    id: q.id,
                    content: q.content,
                    answer: q.answer,
                    explanation: q.explanation,
                  ),
                )
                .toList();

        _currentQuestionIndex = 0;
        debugPrint("获取到${_questionList.length}道题目");
      }

      if (_questionList.isNotEmpty) {
        setState(() {
          _currentQuestion = _questionList[_currentQuestionIndex];
          _currentStage = QuestionStage.displayingQuestion;
          _isCorrect = null;
          _userAnswer = '';

          // 清空聊天气泡并添加欢迎语
          if (_chatBubbles.isEmpty) {
            _addWelcomeBubble();
          }

          // 添加题目气泡
          _addTypingBubble(
            "题目 ${_currentQuestionIndex + 1}/${_questionList.length}: ${_currentQuestion!.content}",
            BubbleType.question,
          );
        });
      } else {
        setState(() {
          _errorMessage = "未能获取题目";
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 添加欢迎气泡
  void _addWelcomeBubble() {
    final welcomeMessages = [
      "你好！我是你的学习助手。今天让我们一起学习${widget.courseName}吧！",
      "准备好了吗？让我们开始${widget.courseName}的学习之旅！",
      "欢迎进入${widget.courseName}的学习模式！我会陪你一起成长！",
    ];

    final random = Random();
    final message = welcomeMessages[random.nextInt(welcomeMessages.length)];

    _addTypingBubble(message, BubbleType.ai);
  }

  /// 添加一个带有打字效果的气泡
  void _addTypingBubble(String message, BubbleType type) {
    // 添加一个空的打字气泡
    setState(() {
      _chatBubbles.add(ChatBubble(message: '', type: type, isTyping: true));
      _currentTypingBubbleIndex = _chatBubbles.length - 1;
    });

    // 模拟打字效果
    _simulateTyping(message);
  }

  /// 模拟打字效果
  void _simulateTyping(String message) {
    // 取消之前的定时器
    _typingTimer?.cancel();

    var currentText = '';
    final typingSpeed = 30; // 每个字符的毫秒数
    int i = 0;

    _typingTimer = Timer.periodic(Duration(milliseconds: typingSpeed), (timer) {
      if (i < message.length) {
        currentText += message[i];
        i++;

        if (mounted && _currentTypingBubbleIndex != null) {
          setState(() {
            _chatBubbles[_currentTypingBubbleIndex!] = ChatBubble(
              message: currentText,
              type: _chatBubbles[_currentTypingBubbleIndex!].type,
              isTyping: true,
            );
          });
        }
      } else {
        // 打字完成
        if (mounted && _currentTypingBubbleIndex != null) {
          setState(() {
            _chatBubbles[_currentTypingBubbleIndex!] = ChatBubble(
              message: currentText,
              type: _chatBubbles[_currentTypingBubbleIndex!].type,
              isTyping: false,
            );
            _currentTypingBubbleIndex = null;
          });

          if (_chatBubbles.last.type == BubbleType.question) {
            // 如果是题目，则进入等待答题阶段
            _currentStage = QuestionStage.waitingForAnswer;
          }
        }
        timer.cancel();
        _scrollToBottom();
      }
    });
  }

  /// 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onVerticalDragUpdate: (details) {
        // 向下拖动才处理
        if (details.primaryDelta! > 0) {
          // 将拖动转换为进度值（0-1）
          double dragProgress = details.primaryDelta! / 20; // 敏感度系数
          _dragController.value += dragProgress;

          // 限制最大值为1
          if (_dragController.value > 1) {
            _dragController.value = 1;
          }
        }
      },
      onVerticalDragEnd: (details) {
        // 如果拖拽超过阈值或有足够的速度，关闭页面
        if (_dragController.value > 0.2 ||
            (details.primaryVelocity != null &&
                details.primaryVelocity! > 700)) {
          _dragController.forward().then((_) {
            Navigator.of(context).pop();
          });
        } else {
          // 否则恢复原状
          _dragController.reverse();
        }
      },
      child: AnimatedBuilder(
        animation: _dragController,
        builder: (context, child) {
          // 计算拖拽时的偏移和缩放
          final double offset =
              MediaQuery.of(context).size.height * 0.5 * _dragController.value;
          final double scale = 1.0 - (_dragController.value * 0.05);

          return Transform.translate(
            offset: Offset(0, offset),
            child: Transform.scale(
              scale: scale,
              child: Opacity(
                opacity: 1.0 - (_dragController.value * 0.5),
                child: child,
              ),
            ),
          );
        },
        child: Container(
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                spreadRadius: 5,
              ),
            ],
          ),
          child: Column(
            children: [
              // 拖动指示器 - 保留这个以支持下拉手势的视觉提示
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                height: 5,
                width: 40,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              Expanded(
                child: Scaffold(
                  key: _scaffoldKey,
                  backgroundColor: Colors.transparent,
                  // 移除AppBar，使用自定义顶部区域
                  appBar: null,
                  endDrawer: _buildSidebar(),
                  body: Stack(
                    children: [
                      // 主体内容
                      _buildBody(),

                      // 右上角菜单按钮（替代AppBar的actions）
                      Positioned(
                        top:
                            MediaQuery.of(context).viewPadding.top +
                            15, // 考虑状态栏高度
                        right: 15,
                        child: GestureDetector(
                          onTap: () {
                            _scaffoldKey.currentState?.openEndDrawer();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(alpha: 0.7),
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Container(
                                  width: 20,
                                  height: 2.5,
                                  margin: const EdgeInsets.only(bottom: 5),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                Container(
                                  width: 20,
                                  height: 2.5,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建侧边栏
  Widget _buildSidebar() {
    return Drawer(
      child: Container(
        color: const Color(0xFF121212),
        child: Column(
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(color: Color(0xFF212121)),
              child: Center(
                child: Text(
                  widget.courseName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            Expanded(
              child:
                  _isLoading && _categoryTabs.isEmpty
                      ? const CategoryTabsShimmerLoading()
                      : _categoryTabs.isEmpty
                      ? const Center(
                        child: Text(
                          "暂无分类",
                          style: TextStyle(color: Colors.white70),
                        ),
                      )
                      : ListView.builder(
                        itemCount: _categoryTabs.length,
                        itemBuilder: (context, index) {
                          final tab = _categoryTabs[index];
                          final isSelected = _selectedTab?.id == tab.id;

                          return ListTile(
                            title: Text(
                              tab.name,
                              style: TextStyle(
                                color: isSelected ? Colors.red : Colors.white,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                              ),
                            ),
                            leading: Icon(
                              _getTabIcon(tab.tabType),
                              color: isSelected ? Colors.red : Colors.white70,
                            ),
                            onTap: () {
                              Navigator.pop(context);
                              if (_selectedTab?.id != tab.id) {
                                if (widget.preloadedData) {
                                  _loadPreloadedTabContent(tab);
                                } else {
                                  _loadTabContent(tab);
                                }
                              }
                            },
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  /// 根据标签类型获取图标
  IconData _getTabIcon(String tabType) {
    switch (tabType) {
      case 'quiz':
        return Icons.quiz;
      case 'article':
        return Icons.article;
      case 'video':
        return Icons.video_library;
      default:
        return Icons.folder;
    }
  }

  /// 构建主体
  Widget _buildBody() {
    if (_isLoading && _chatBubbles.isEmpty) {
      return const QuizShimmerLoading();
    }

    if (_errorMessage != null && _chatBubbles.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed:
                    widget.preloadedData
                        ? _loadPreloadedData
                        : _loadCategoryTabs,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text("重试"),
              ),
            ],
          ),
        ),
      );
    }

    // 使用SafeArea，确保内容不会被系统UI遮挡
    return SafeArea(
      child: Column(
        children: [
          // 添加一个隐形的顶部空间，替代AppBar
          const SizedBox(height: 40),

          Expanded(child: _buildChatArea()),
          _buildInputArea(),
        ],
      ),
    );
  }

  /// 构建聊天区域
  Widget _buildChatArea() {
    return ListView.builder(
      controller: _scrollController,
      // 增加顶部内边距，为顶部留出空间
      padding: const EdgeInsets.fromLTRB(16.0, 10.0, 16.0, 16.0),
      physics: const BouncingScrollPhysics(), // 使用回弹效果的物理滚动
      itemCount: _chatBubbles.length,
      itemBuilder: (context, index) {
        final bubble = _chatBubbles[index];
        return _buildChatBubble(bubble);
      },
    );
  }

  /// 构建聊天气泡
  Widget _buildChatBubble(ChatBubble bubble) {
    final isUserBubble =
        bubble.type == BubbleType.user || bubble.type == BubbleType.answer;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment:
            isUserBubble ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUserBubble) _buildAvatar(bubble.type),
          const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: _getBubbleColor(bubble.type),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    bubble.message,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  if (bubble.isTyping)
                    const Padding(
                      padding: EdgeInsets.only(top: 8.0),
                      child: SizedBox(
                        width: 12,
                        height: 12,
                        child: CupertinoActivityIndicator(
                          radius: 8,
                          color: CupertinoColors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isUserBubble) _buildAvatar(bubble.type),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(BubbleType type) {
    final isUser = type == BubbleType.user || type == BubbleType.answer;

    return CircleAvatar(
      backgroundColor: isUser ? Colors.blue : Colors.red,
      radius: 16,
      child: Icon(
        isUser ? Icons.person : Icons.android,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  /// 获取气泡颜色
  Color _getBubbleColor(BubbleType type) {
    switch (type) {
      case BubbleType.ai:
        return const Color(0xFF8B0000);
      case BubbleType.user:
        return const Color(0xFF303F9F);
      case BubbleType.question:
        return const Color(0xFF4A148C);
      case BubbleType.answer:
        return const Color(0xFF303F9F);
      case BubbleType.explanation:
        return const Color(0xFF006064);
      case BubbleType.aiResponse:
        return const Color(0xFF8B0000);
    }
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    // 根据当前阶段显示不同的输入区域
    switch (_currentStage) {
      case QuestionStage.waitingForAnswer:
        return _buildAnswerInput();
      case QuestionStage.waitingForQuestion:
        return _buildQuestionInput();
      default:
        return const SizedBox.shrink(); // 其他阶段不显示输入框
    }
  }

  /// 构建答题输入区域
  Widget _buildAnswerInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: const Color(0xFF1A1A1A),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _answerController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                hintText: "输入你的答案...",
                hintStyle: TextStyle(color: Colors.white54),
                border: InputBorder.none,
              ),
              onChanged: (value) {
                _userAnswer = value;
              },
            ),
          ),
          IconButton(
            icon: const Icon(Icons.send, color: Colors.red),
            onPressed: () {
              if (_answerController.text.trim().isNotEmpty) {
                _submitAnswer();
              }
            },
          ),
        ],
      ),
    );
  }

  /// 构建提问输入区域
  Widget _buildQuestionInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      color: const Color(0xFF1A1A1A),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _questionController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                hintText: "有疑问吗？问我吧...",
                hintStyle: TextStyle(color: Colors.white54),
                border: InputBorder.none,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.send, color: Colors.red),
            onPressed: () {
              if (_questionController.text.trim().isNotEmpty) {
                _submitQuestion();
              }
            },
          ),
          TextButton(
            onPressed: _proceedToNextQuestion,
            child: const Text("下一题", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// 提交答案
  void _submitAnswer() {
    final answer = _answerController.text.trim();
    if (answer.isEmpty) return;

    setState(() {
      _chatBubbles.add(ChatBubble(message: answer, type: BubbleType.answer));
      _currentStage = QuestionStage.displayingExplanation;
      _userAnswer = answer;
      _answerController.clear();
    });

    _scrollToBottom();

    // 检查答案
    _checkAnswer();
  }

  /// 检查答案
  void _checkAnswer() {
    if (_currentQuestion == null) return;

    // 检查答案是否正确
    final String answerLower = _userAnswer.toLowerCase();
    final String correctLower = _currentQuestion!.answer?.toLowerCase() ?? '';
    final isCorrect = answerLower == correctLower;
    setState(() {
      _isCorrect = isCorrect;
    });

    // 添加评价气泡
    final feedbackMessage =
        isCorrect ? "回答正确！👍" : "回答不正确。正确答案是: ${_currentQuestion!.answer}";

    _addTypingBubble(feedbackMessage, BubbleType.ai);

    // 添加解析气泡
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        _addTypingBubble(
          "解析: ${_currentQuestion!.explanation}",
          BubbleType.explanation,
        );

        // 进入等待提问阶段
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (mounted) {
            setState(() {
              _currentStage = QuestionStage.waitingForQuestion;
            });
          }
        });
      }
    });
  }

  /// 提交问题
  void _submitQuestion() {
    final question = _questionController.text.trim();
    if (question.isEmpty) return;

    setState(() {
      _chatBubbles.add(ChatBubble(message: question, type: BubbleType.user));
      _currentStage = QuestionStage.displayingAiResponse;
      _questionController.clear();
    });

    _scrollToBottom();

    // 模拟AI回答
    _generateAiResponse(question);
  }

  /// 生成AI回答
  void _generateAiResponse(String question) {
    // 这里可以调用后端API获取AI回答
    // 现在我们使用模拟数据
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final responses = [
          "这是一个很好的问题！${_currentQuestion!.explanation}",
          "让我来解释一下。${_currentQuestion!.explanation}",
          "我理解你的困惑。简单来说，${_currentQuestion!.explanation}",
        ];

        final random = Random();
        final response = responses[random.nextInt(responses.length)];

        _addTypingBubble(response, BubbleType.aiResponse);

        // 回到等待提问阶段
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (mounted) {
            setState(() {
              _currentStage = QuestionStage.waitingForQuestion;
            });
          }
        });
      }
    });
  }

  /// 进入下一题
  void _proceedToNextQuestion() {
    setState(() {
      _currentStage = QuestionStage.preparingNextQuestion;
    });

    // 添加过渡语
    final transitionMessages = ["让我们继续下一题...", "好的，接下来是...", "准备好了吗？下一题来了..."];

    final random = Random();
    final message =
        transitionMessages[random.nextInt(transitionMessages.length)];

    _addTypingBubble(message, BubbleType.ai);

    // 进入下一题
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        _currentQuestionIndex++;
        _fetchNextQuestion();
      }
    });
  }

  /// 加载预加载的数据
  void _loadPreloadedData() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn || userProvider.token == null) {
      _loadCategoryTabs();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // 使用预加载的分类标签数据，添加额外的超时保护
    final tabsFuture = _preloadService
        .preloadCategoryTabs(widget.courseId, userProvider.token!)
        .timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            debugPrint("⚠️ 获取预加载标签超时，使用默认标签（测试）");
            // 超时时使用默认标签（测试）
            return [
              QuizPageCategoryTab(
                id: 0,
                name: "答题（测试）",
                description: "练习答题",
                level4Id: widget.courseId,
                isActive: true,
                tabType: "quiz",
                englishName: "Quiz (Test)",
              ),
              QuizPageCategoryTab(
                id: 1,
                name: "错题本（测试）",
                description: "查看错题",
                level4Id: widget.courseId,
                isActive: true,
                tabType: "wrong_questions",
                englishName: "Wrong Questions (Test)",
              ),
              QuizPageCategoryTab(
                id: 2,
                name: "数据面板（测试）",
                description: "答题统计",
                level4Id: widget.courseId,
                isActive: true,
                tabType: "stats",
                englishName: "Statistics (Test)",
              ),
            ];
          },
        );

    tabsFuture
        .then((loadedTabs) {
          if (mounted) {
            setState(() {
              _categoryTabs = loadedTabs;
              debugPrint("✅ 使用预加载的标签页数量: ${_categoryTabs.length}");

              // 如果有标签页，选择第一个并加载内容
              if (_categoryTabs.isNotEmpty) {
                // 默认选择quiz类型标签，如果没有则选第一个
                _selectedTab = _categoryTabs.firstWhere(
                  (tab) => tab.tabType == 'quiz',
                  orElse: () => _categoryTabs.first,
                );

                // 如果是quiz类型，尝试使用预加载的问题数据
                if (_selectedTab!.tabType == 'quiz') {
                  _loadPreloadedTabContent(_selectedTab!);
                } else {
                  _loadTabContent(_selectedTab!);
                }
              } else {
                // 没有标签页时，直接获取题目
                _loadPreloadedQuestions();
              }
            });
          }
        })
        .catchError((error) {
          debugPrint("⚠️ 使用预加载数据失败: $error");
          // 如果预加载数据使用失败，回退到常规加载
          _loadCategoryTabs();
        });
  }

  /// 加载预加载的标签内容
  void _loadPreloadedTabContent(QuizPageCategoryTab tab) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn || userProvider.token == null) {
      _loadTabContent(tab);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // 使用预加载的标签内容数据
    _preloadService
        .preloadTabContent(tab, userProvider.token!)
        .then((content) {
          if (mounted) {
            setState(() {
              _questionSets = content.cast<QuizPageQuestionSet>();
              debugPrint("✅ 使用预加载的题库数量: ${_questionSets.length}");

              // 如果有题库，选择第一个
              if (_questionSets.isNotEmpty) {
                _selectedQuestionSet = _questionSets.first;
                _loadPreloadedQuestions();
              }
              _isLoading = false;
            });
          }
        })
        .catchError((error) {
          debugPrint("⚠️ 使用预加载标签内容失败: $error");
          // 回退到常规加载
          _loadTabContent(tab);
        });
  }

  /// 加载预加载的问题数据
  void _loadPreloadedQuestions() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn || userProvider.token == null) {
      _fetchNextQuestion();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // 使用预加载的问题数据
    _preloadService
        .preloadQuestions(widget.courseId, userProvider.token!)
        .then((questions) {
          if (mounted) {
            setState(() {
              _questionList = questions;
              debugPrint("✅ 使用预加载的问题数量: ${_questionList.length}");

              if (_questionList.isNotEmpty) {
                _currentQuestionIndex = 0;
                _currentQuestion = _questionList[_currentQuestionIndex];
                _currentStage = QuestionStage.displayingQuestion;
                _isCorrect = null;
                _userAnswer = '';

                // 清空聊天气泡并添加欢迎语
                if (_chatBubbles.isEmpty) {
                  _addWelcomeBubble();
                }

                // 添加题目气泡
                _addTypingBubble(
                  "题目 ${_currentQuestionIndex + 1}/${_questionList.length}: ${_currentQuestion!.content}",
                  BubbleType.question,
                );
              }
              _isLoading = false;
            });
          }
        })
        .catchError((error) {
          debugPrint("⚠️ 使用预加载问题数据失败: $error");
          // 回退到常规加载
          _fetchNextQuestion();
        });
  }
}
