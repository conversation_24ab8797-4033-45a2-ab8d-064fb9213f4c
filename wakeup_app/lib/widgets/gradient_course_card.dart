import 'package:flutter/material.dart';
import 'package:mesh/mesh.dart';

class GradientCourseCard extends StatefulWidget {
  final String courseName;
  final String? description;
  final String? highlight;
  final VoidCallback? onTap;
  final double borderRadius;
  final double height;
  final int? categoryId;

  const GradientCourseCard({
    super.key,
    required this.courseName,
    this.description,
    this.highlight,
    this.onTap,
    this.borderRadius = 18.0,
    this.height = 140.0,
    this.categoryId,
  });

  @override
  State<GradientCourseCard> createState() => _GradientCourseCardState();
}

class _GradientCourseCardState extends State<GradientCourseCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  // 极光渐变配色系统（采用示例文件的线性/径向渐变方案）
  Map<String, dynamic> get gradientTheme {
    final List<Map<String, dynamic>> themes = [
      // 以下主题不再使用 Mesh；而是 colors + auroraColors + blurOverlay
      // 紫色极光系 - 动态紫色极光效果
      {
        'meshRect': OMeshRect(
          width: 5,
          height: 5,
          colorSpace: OMeshColorSpace.lab,
          fallbackColor: const Color(0xFF030108),
          backgroundColor: const Color(0xFF030108),
          vertices: [
            // 第一行 - 深邃夜空（最暗）
            (-0.05, -0.02).v,
            (0.22, -0.04).v,
            (0.48, -0.01).v,
            (0.76, -0.03).v,
            (1.05, -0.02).v,

            // 第二行 - 极光初现（微光）- 波浪形飘逸曲线
            (-0.08, 0.20).v,
            (0.18, 0.18).v.bezier(east: (0.28, 0.15).v, west: (0.08, 0.22).v),
            (0.45, 0.23).v.bezier(east: (0.55, 0.20).v, west: (0.35, 0.26).v),
            (0.72, 0.19).v.bezier(east: (0.82, 0.16).v, west: (0.62, 0.22).v),
            (1.08, 0.21).v,

            // 第三行 - 极光渐强（中等亮度）- 波浪形流动
            (-0.1, 0.47).v,
            (0.15, 0.45).v.bezier(east: (0.25, 0.42).v, west: (0.05, 0.48).v),
            (0.42, 0.50).v.bezier(east: (0.55, 0.47).v, west: (0.32, 0.53).v),
            (0.78, 0.46).v.bezier(east: (0.88, 0.43).v, west: (0.68, 0.49).v),
            (1.1, 0.48).v,

            // 第四行 - 极光强烈（高亮）- 波浪形爆发
            (-0.08, 0.74).v,
            (0.24, 0.72).v.bezier(east: (0.36, 0.69).v, west: (0.12, 0.75).v),
            (0.52, 0.77).v.bezier(east: (0.64, 0.74).v, west: (0.40, 0.80).v),
            (0.79, 0.73).v.bezier(east: (0.91, 0.70).v, west: (0.67, 0.76).v),
            (1.08, 0.75).v,

            // 第五行 - 极光最强（最亮）
            (-0.05, 1.02).v,
            (0.25, 1.04).v,
            (0.52, 1.01).v,
            (0.78, 1.03).v,
            (1.05, 1.02).v,
          ],
          colors: const [
            // 第一行 - 边缘深邃背景（最暗）
            Color(0xFF010005),
            Color(0xFF020108),
            Color(0xFF040210),
            Color(0xFF020106),
            Color(0xFF010005),

            // 第二行 - 早期紫光渗透
            Color(0xFF060318),
            Color(0xFF15072D),
            Color(0xFF280D45),
            Color(0xFF18082F),
            Color(0xFF060318),

            // 第三行 - 中等强度过渡
            Color(0xFF180829),
            Color(0xFF241250),
            Color(0xFF3D1570),
            Color(0xFF261355),
            Color(0xFF1A092E),

            // 第四行 - 强烈极光爆发
            Color(0xFF3D1570),
            Color(0xFFB050FF),
            Color(0xFFFF3090),
            Color(0xFF9040E0),
            Color(0xFF421875),

            // 第五行 - 最强极光绽放（最亮）
            Color(0xFF5015A0),
            Color(0xFFFF40FF),
            Color(0xFFE020FF),
            Color(0xFFFF1080),
            Color(0xFF6020A0),
          ],
        ),
        'shadowColor': Color(0xFF280D45),
        'highlightColor': Color(0xFFFF40FF).withValues(alpha: 0.95),
      },
      // 蓝色极光系 - 冰晶蓝极光效果
      {
        'meshRect': OMeshRect(
          width: 5,
          height: 5,
          colorSpace: OMeshColorSpace.lab,
          fallbackColor: const Color(0xFF000308),
          backgroundColor: const Color(0xFF000308),
          vertices: [
            // 第一行 - 冰晶边缘流动
            (-0.06, -0.03).v,
            (0.24, -0.05).v,
            (0.51, -0.02).v,
            (0.75, -0.04).v,
            (1.06, -0.03).v,

            // 第二行 - 冰晶边缘渗透 - 锯齿形冰晶折射
            (-0.09, 0.19).v,
            (0.19, 0.16).v,
            (0.48, 0.22).v,
            (0.73, 0.17).v,
            (1.09, 0.20).v,

            // 第三行 - 冰蓝爆发核心 - 锯齿形冰晶破裂
            (-0.11, 0.46).v,
            (0.16, 0.43).v,
            (0.46, 0.51).v,
            (0.77, 0.44).v,
            (1.11, 0.49).v,

            // 第四行 - 冰光边缘扩散 - 锯齿形光芒散射
            (-0.09, 0.77).v,
            (0.22, 0.74).v,
            (0.54, 0.80).v,
            (0.81, 0.75).v,
            (1.09, 0.78).v,

            // 第五行 - 冰海边缘流淌
            (-0.06, 1.03).v,
            (0.26, 1.05).v,
            (0.53, 1.02).v,
            (0.79, 1.04).v,
            (1.06, 1.03).v,
          ],
          colors: const [
            // 第一行 - 边缘深邃背景（最暗）
            Color(0xFF000105),
            Color(0xFF000308),
            Color(0xFF020818),
            Color(0xFF000206),
            Color(0xFF000105),

            // 第二行 - 冰晶形成渗透
            Color(0xFF031020),
            Color(0xFF0A1540),
            Color(0xFF152A65),
            Color(0xFF0C1742),
            Color(0xFF031020),

            // 第三行 - 中等强度过渡
            Color(0xFF0C1742),
            Color(0xFF132765),
            Color(0xFF204080),
            Color(0xFF152968),
            Color(0xFF0E1944),

            // 第二行 - 强烈极光爆发
            Color(0xFF152A65),
            Color(0xFF40C0FF),
            Color(0xFF20F0FF),
            Color(0xFF50B0FF),
            Color(0xFF1B2F68),

            // 第五行 - 最强极光绿放（最亮）
            Color(0xFF204080),
            Color(0xFF00FFFF),
            Color(0xFF30D0FF),
            Color(0xFF00A0FF),
            Color(0xFF1080C0),
          ],
        ),
        'shadowColor': Color(0xFF152A65),
        'highlightColor': Color(0xFF00FFFF).withValues(alpha: 0.95),
      },
      // 绿色极光系 - 生命翡翠极光效果
      {
        'meshRect': OMeshRect(
          width: 5,
          height: 5,
          colorSpace: OMeshColorSpace.lab,
          fallbackColor: const Color(0xFF020805),
          backgroundColor: const Color(0xFF020805),
          vertices: [
            // 第一行 - 森林边缘流动
            (-0.07, -0.02).v,
            (0.23, -0.04).v,
            (0.49, -0.01).v,
            (0.77, -0.03).v,
            (1.07, -0.02).v,

            // 第二行 - 生命边缘渗透 - 有机曲线生命律动
            (-0.1, 0.21).v,
            (0.17, 0.19).v.bezier(east: (0.30, 0.16).v, west: (0.04, 0.22).v),
            (0.47, 0.24).v.bezier(east: (0.60, 0.21).v, west: (0.34, 0.27).v),
            (0.74, 0.18).v.bezier(east: (0.87, 0.15).v, west: (0.61, 0.21).v),
            (1.1, 0.20).v,

            // 第三行 - 翡翠能量爆发 - 有机曲线能量流动
            (-0.12, 0.49).v,
            (0.14, 0.46).v.bezier(east: (0.28, 0.43).v, west: (0.00, 0.49).v),
            (0.44, 0.52).v.bezier(east: (0.58, 0.49).v, west: (0.30, 0.55).v),
            (0.76, 0.45).v.bezier(east: (0.90, 0.42).v, west: (0.62, 0.48).v),
            (1.12, 0.47).v,

            // 第四行 - 生命边缘扩散 - 有机曲线生命绿放
            (-0.1, 0.76).v,
            (0.21, 0.73).v.bezier(east: (0.35, 0.70).v, west: (0.07, 0.76).v),
            (0.51, 0.79).v.bezier(east: (0.65, 0.76).v, west: (0.37, 0.82).v),
            (0.82, 0.74).v.bezier(east: (0.96, 0.71).v, west: (0.68, 0.77).v),
            (1.1, 0.75).v,

            // 第五行 - 森林边缘流淌
            (-0.07, 1.04).v,
            (0.24, 1.06).v,
            (0.51, 1.03).v,
            (0.78, 1.05).v,
            (1.07, 1.04).v,
          ],
          colors: const [
            // 第一行 - 边缘深邃背景（最暗）
            Color(0xFF010403),
            Color(0xFF020805),
            Color(0xFF051810),
            Color(0xFF020604),
            Color(0xFF010403),

            // 第二行 - 生命萌芽渗透
            Color(0xFF082015),
            Color(0xFF154035),
            Color(0xFF2A6550),
            Color(0xFF174238),
            Color(0xFF082015),

            // 第三行 - 中等强度过渡
            Color(0xFF174238),
            Color(0xFF235255),
            Color(0xFF357570),
            Color(0xFF255458),
            Color(0xFF194440),

            // 第四行 - 强烈极光爆发
            Color(0xFF2A6550),
            Color(0xFF60FF88),
            Color(0xFF30E080),
            Color(0xFF70D090),
            Color(0xFF2F6855),

            // 第五行 - 最强极光绿放（最亮）
            Color(0xFF357570),
            Color(0xFF00FF80),
            Color(0xFF40FF90),
            Color(0xFF20C070),
            Color(0xFF30A050),
          ],
        ),
        'shadowColor': Color(0xFF2A6550),
        'highlightColor': Color(0xFF00FF80).withValues(alpha: 0.95),
      },
      // 橙色极光系 - 火焰橙极光效果
      {
        'meshRect': OMeshRect(
          width: 5,
          height: 5,
          colorSpace: OMeshColorSpace.lab,
          fallbackColor: const Color(0xFF080302),
          backgroundColor: const Color(0xFF080302),
          vertices: [
            // 第一行 - 火焰边缘流动
            (-0.08, -0.01).v,
            (0.25, -0.03).v,
            (0.48, 0.01).v,
            (0.74, -0.04).v,
            (1.08, -0.01).v,

            // 第二行 - 火焰边缘渗透 - 火焰形上升热流
            (-0.11, 0.22).v,
            (0.20, 0.18).v.bezier(east: (0.32, 0.14).v, west: (0.08, 0.22).v),
            (0.46, 0.25).v.bezier(east: (0.58, 0.21).v, west: (0.34, 0.29).v),
            (0.72, 0.19).v.bezier(east: (0.84, 0.15).v, west: (0.60, 0.23).v),
            (1.11, 0.21).v,

            // 第三行 - 火焰核心爆发 - 火焰形热浪翻腾
            (-0.13, 0.51).v,
            (0.15, 0.47).v.bezier(east: (0.29, 0.43).v, west: (0.01, 0.51).v),
            (0.45, 0.54).v.bezier(east: (0.59, 0.50).v, west: (0.31, 0.58).v),
            (0.78, 0.48).v.bezier(east: (0.92, 0.44).v, west: (0.64, 0.52).v),
            (1.13, 0.49).v,

            // 第四行 - 火焰边缘蔓延 - 火焰形热力四射
            (-0.11, 0.78).v,
            (0.23, 0.75).v.bezier(east: (0.37, 0.71).v, west: (0.09, 0.79).v),
            (0.53, 0.81).v.bezier(east: (0.67, 0.77).v, west: (0.39, 0.85).v),
            (0.80, 0.76).v.bezier(east: (0.94, 0.72).v, west: (0.66, 0.80).v),
            (1.11, 0.77).v,

            // 第五行 - 火焰边缘流淌
            (-0.08, 1.05).v,
            (0.25, 1.07).v,
            (0.52, 1.04).v,
            (0.77, 1.06).v,
            (1.08, 1.05).v,
          ],
          colors: const [
            // 第一行 - 边缘深邃背景（最暗）
            Color(0xFF040201),
            Color(0xFF080302),
            Color(0xFF100804),
            Color(0xFF060201),
            Color(0xFF040201),

            // 第二行 - 火花初现渗透
            Color(0xFF201008),
            Color(0xFF402018),
            Color(0xFF653528),
            Color(0xFF42221A),
            Color(0xFF201008),

            // 第三行 - 中等强度过渡
            Color(0xFF42221A),
            Color(0xFF633222),
            Color(0xFF854530),
            Color(0xFF653424),
            Color(0xFF44241C),

            // 第四行 - 强烈极光爆发
            Color(0xFF653528),
            Color(0xFFFF9040),
            Color(0xFFFFB030),
            Color(0xFFFF7020),
            Color(0xFF6A372B),

            // 第五行 - 最强极光绿放（最亮）
            Color(0xFF854530),
            Color(0xFFFF4000),
            Color(0xFFFF8020),
            Color(0xFFFF6010),
            Color(0xFFB05020),
          ],
        ),
        'shadowColor': Color(0xFF653528),
        'highlightColor': Color(0xFFFF4000).withValues(alpha: 0.95),
      },
      // 粉色极光系 - 樱花粉极光效果
      {
        'meshRect': OMeshRect(
          width: 5,
          height: 5,
          colorSpace: OMeshColorSpace.lab,
          fallbackColor: const Color(0xFF080205),
          backgroundColor: const Color(0xFF080205),
          vertices: [
            // 第一行 - 樱花边缘流动
            (-0.09, -0.02).v,
            (0.26, -0.04).v,
            (0.50, 0.00).v,
            (0.75, -0.03).v,
            (1.09, -0.02).v,

            // 第二行 - 樱花边缘渗透 - 柔和曲线樱花飘散
            (-0.12, 0.23).v,
            (0.18, 0.20).v.bezier(east: (0.32, 0.17).v, west: (0.04, 0.23).v),
            (0.49, 0.26).v.bezier(east: (0.63, 0.23).v, west: (0.35, 0.29).v),
            (0.71, 0.21).v.bezier(east: (0.85, 0.18).v, west: (0.57, 0.24).v),
            (1.12, 0.22).v,

            // 第三行 - 樱花绽放核心 - 柔和曲线花瓣绽放
            (-0.14, 0.50).v,
            (0.13, 0.47).v.bezier(east: (0.27, 0.44).v, west: (-0.01, 0.50).v),
            (0.47, 0.53).v.bezier(east: (0.61, 0.50).v, west: (0.33, 0.56).v),
            (0.79, 0.48).v.bezier(east: (0.93, 0.45).v, west: (0.65, 0.51).v),
            (1.14, 0.49).v,

            // 第四行 - 樱花边缘扩散 - 柔和曲线樱雨漂洒
            (-0.12, 0.80).v,
            (0.24, 0.77).v.bezier(east: (0.38, 0.74).v, west: (0.10, 0.80).v),
            (0.55, 0.83).v.bezier(east: (0.69, 0.80).v, west: (0.41, 0.86).v),
            (0.83, 0.78).v.bezier(east: (0.97, 0.75).v, west: (0.69, 0.81).v),
            (1.12, 0.79).v,

            // 第五行 - 樱花边缘流淌
            (-0.09, 1.06).v,
            (0.27, 1.08).v,
            (0.54, 1.05).v,
            (0.81, 1.07).v,
            (1.09, 1.06).v,
          ],
          colors: const [
            // 第一行 - 边缘深邃背景（最暗）
            Color(0xFF040103),
            Color(0xFF080205),
            Color(0xFF100410),
            Color(0xFF060104),
            Color(0xFF040103),

            // 第二行 - 樱花气息渗透
            Color(0xFF200818),
            Color(0xFF401530),
            Color(0xFF652548),
            Color(0xFF421732),
            Color(0xFF200818),

            // 第三行 - 中等强度过渡
            Color(0xFF421732),
            Color(0xFF522242),
            Color(0xFF753560),
            Color(0xFF542444),
            Color(0xFF441934),

            // 第四行 - 强烈极光爆发
            Color(0xFF652548),
            Color(0xFFFF70B0),
            Color(0xFFFF40D0),
            Color(0xFFE060A0),
            Color(0xFF6A284B),

            // 第五行 - 最强极光绿放（最亮）
            Color(0xFF753560),
            Color(0xFFFF20C0),
            Color(0xFFFF50A0),
            Color(0xFFFF3080),
            Color(0xFFB04080),
          ],
        ),
        'shadowColor': Color(0xFF652548),
        'highlightColor': Color(0xFFFF20C0).withValues(alpha: 0.95),
      },
      // 青色极光系 - 海洋青极光效果
      {
        'meshRect': OMeshRect(
          width: 5,
          height: 5,
          colorSpace: OMeshColorSpace.lab,
          fallbackColor: const Color(0xFF020808),
          backgroundColor: const Color(0xFF020808),
          vertices: [
            // 第一行 - 海洋边缘流动
            (-0.1, -0.03).v,
            (0.22, -0.05).v,
            (0.52, 0.00).v,
            (0.76, -0.03).v,
            (1.1, -0.03).v,

            // 第二行 - 海洋边缘渗透 - 流水形海浪涌动
            (-0.13, 0.24).v,
            (0.21, 0.21).v.bezier(east: (0.35, 0.18).v, west: (0.07, 0.24).v),
            (0.45, 0.27).v.bezier(east: (0.59, 0.24).v, west: (0.31, 0.30).v),
            (0.73, 0.22).v.bezier(east: (0.87, 0.19).v, west: (0.59, 0.25).v),
            (1.13, 0.23).v,

            // 第三行 - 海洋极光核心 - 流水形海潮起伏
            (-0.15, 0.52).v,
            (0.17, 0.49).v.bezier(east: (0.31, 0.46).v, west: (0.03, 0.52).v),
            (0.43, 0.55).v.bezier(east: (0.57, 0.52).v, west: (0.29, 0.58).v),
            (0.75, 0.50).v.bezier(east: (0.89, 0.47).v, west: (0.61, 0.53).v),
            (1.15, 0.51).v,

            // 第四行 - 海洋边缘扩散 - 流水形浪花飞溅
            (-0.13, 0.82).v,
            (0.25, 0.79).v.bezier(east: (0.39, 0.76).v, west: (0.11, 0.82).v),
            (0.56, 0.85).v.bezier(east: (0.70, 0.82).v, west: (0.42, 0.88).v),
            (0.84, 0.80).v.bezier(east: (0.98, 0.77).v, west: (0.70, 0.83).v),
            (1.13, 0.81).v,

            // 第五行 - 海洋边缘流淌
            (-0.1, 1.07).v,
            (0.23, 1.09).v,
            (0.55, 1.06).v,
            (0.78, 1.08).v,
            (1.1, 1.07).v,
          ],
          colors: const [
            // 第一行 - 边缘深邃背景（最暗）
            Color(0xFF010404),
            Color(0xFF020808),
            Color(0xFF041018),
            Color(0xFF020606),
            Color(0xFF010404),

            // 第二行 - 海潮涌动渗透
            Color(0xFF081520),
            Color(0xFF152840),
            Color(0xFF2A5065),
            Color(0xFF172A42),
            Color(0xFF081520),

            // 第三行 - 中等强度过渡
            Color(0xFF172A42),
            Color(0xFF235265),
            Color(0xFF358080),
            Color(0xFF255468),
            Color(0xFF192C44),

            // 第四行 - 强烈极光爆发
            Color(0xFF2A5065),
            Color(0xFF60E0FF),
            Color(0xFF40FFFF),
            Color(0xFF80D0FF),
            Color(0xFF2F5368),

            // 第五行 - 最强极光绿放（最亮）
            Color(0xFF358080),
            Color(0xFF00FFFF),
            Color(0xFF20F0E0),
            Color(0xFF40C0FF),
            Color(0xFF30A0C0),
          ],
        ),
        'shadowColor': Color(0xFF2A5065),
        'highlightColor': Color(0xFF00FFFF).withValues(alpha: 0.95),
      },
    ];

    // 根据课程名称或分类ID选择主题
    int index = 0;
    if (widget.categoryId != null) {
      index = widget.categoryId! % themes.length;
    } else {
      index = widget.courseName.hashCode.abs() % themes.length;
    }

    return themes[index];
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = gradientTheme;
    final meshRect = theme['meshRect'] as OMeshRect;
    final shadowColor = theme['shadowColor'] as Color;
    final highlightColor = theme['highlightColor'] as Color;

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) {
        _animationController.reverse();
        widget.onTap?.call();
      },
      onTapCancel: () => _animationController.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                // 微妙主题色边框
                border: Border.all(
                  color: highlightColor.withValues(alpha: 0.08),
                  width: 1.0,
                ),
                // 极光投影系统（来自示例）：叠加多层发光与深度阴影
                boxShadow: [
                  // 1. 基础深度阴影（再降一点）
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.10),
                    blurRadius: 12.0,
                    offset: const Offset(0, 7),
                    spreadRadius: -2,
                  ),
                  // 2. 主发光投影（再降）
                  BoxShadow(
                    color: highlightColor.withValues(alpha: 0.10),
                    blurRadius: 14.0,
                    offset: const Offset(0, 6),
                    spreadRadius: -3,
                  ),
                  // 3. 次级彩色发光（再降）
                  BoxShadow(
                    color: highlightColor.withValues(alpha: 0.06),
                    blurRadius: 10.0,
                    offset: const Offset(0, 5),
                    spreadRadius: -4,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                child: Stack(
                  children: [
                    // 主要极光网格渐变背景
                    OMeshGradient(
                      size: Size.infinite,
                      tessellation: 15, // 更高精度的网格细分，提升动态效果
                      mesh: meshRect,
                    ),

                    // 边缘高光（细边框光效）
                    Positioned.fill(
                      child: IgnorePointer(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                              widget.borderRadius,
                            ),
                            border: Border.all(
                              color: highlightColor.withValues(
                                alpha: 0.06,
                              ), // 降低边缘高光
                              width: 0.6,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 透明容器层
                    Container(
                      width: double.infinity,
                      height: widget.height,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(
                          widget.borderRadius,
                        ),
                      ),
                    ),

                    // 内容区域
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 18,
                        vertical: 16,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        widget.courseName,
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w700,
                                          color: Colors.white,
                                          shadows: [
                                            Shadow(
                                              color: shadowColor.withValues(
                                                alpha: 0.3,
                                              ),
                                              offset: const Offset(0, 1),
                                              blurRadius: 3,
                                            ),
                                          ],
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                                if (widget.description != null) ...[
                                  const SizedBox(height: 6),
                                  Flexible(
                                    child: Text(
                                      widget.description!,
                                      style: TextStyle(
                                        color: Colors.white.withValues(
                                          alpha: 0.95,
                                        ),
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                        shadows: [
                                          Shadow(
                                            color: shadowColor.withValues(
                                              alpha: 0.2,
                                            ),
                                            offset: const Offset(0, 1),
                                            blurRadius: 2,
                                          ),
                                        ],
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      softWrap: true,
                                    ),
                                  ),
                                ],
                                if (widget.highlight != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    widget.highlight!,
                                    style: TextStyle(
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
