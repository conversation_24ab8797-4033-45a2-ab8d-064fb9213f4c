import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// Apple Music风格的下拉菜单项
class AppleMenuOption {
  final String title;
  final IconData? icon;
  final VoidCallback onTap;
  final bool isSelected;
  final bool isSpecial;
  final Color? customTextColor; // 自定义文本颜色
  final Color? customIconColor; // 自定义图标颜色

  AppleMenuOption({
    required this.title,
    this.icon,
    required this.onTap,
    this.isSelected = false,
    this.isSpecial = false,
    this.customTextColor,
    this.customIconColor,
  });
}

/// Apple Music风格的下拉菜单
class AppleStyleMenu {
  /// 显示下拉菜单
  static Future<void> show({
    required BuildContext context,
    required List<AppleMenuOption> options,
    required Offset position,
  }) async {
    // 计算屏幕宽度（无需持有 overlay 局部变量）

    // 设置菜单宽度和位置
    final double menuWidth = 200.0;
    double left = position.dx - (menuWidth / 2);

    // 确保菜单不会超出屏幕边缘
    final double screenWidth = MediaQuery.of(context).size.width;
    if (left < 16) {
      left = 16;
    } else if (left + menuWidth > screenWidth - 16) {
      left = screenWidth - menuWidth - 16;
    }

    // 声明覆盖层条目变量
    late final OverlayEntry entry;

    // 创建关闭菜单的函数
    void closeMenu() {
      entry.remove();
    }

    // 创建覆盖层条目
    entry = OverlayEntry(
      builder:
          (context) => Stack(
            children: [
              // 透明背景，用于检测点击事件关闭菜单
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: closeMenu,
                  child: Container(color: Colors.transparent),
                ),
              ),

              // 菜单本身
              Positioned(
                top: position.dy + 4,
                left: left,
                width: menuWidth,
                child: _AppleMenuWidget(options: options, onClose: closeMenu),
              ),
            ],
          ),
    );

    // 将覆盖层添加到屏幕
    Overlay.of(context).insert(entry);
  }
}

/// 菜单控件
class _AppleMenuWidget extends StatefulWidget {
  final List<AppleMenuOption> options;
  final VoidCallback onClose;

  const _AppleMenuWidget({required this.options, required this.onClose});

  @override
  _AppleMenuWidgetState createState() => _AppleMenuWidgetState();
}

class _AppleMenuWidgetState extends State<_AppleMenuWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // 创建动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // 创建淡入和缩放动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    // 启动动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            alignment: Alignment.topCenter,
            child: _buildMenuContent(),
          ),
        );
      },
    );
  }

  Widget _buildMenuContent() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFF1C1C1E).withValues(alpha: 0.92),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _buildMenuItems(),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildMenuItems() {
    final List<Widget> items = [];
    bool previousWasSpecial = false;

    for (int i = 0; i < widget.options.length; i++) {
      final option = widget.options[i];

      // 添加特殊分隔区域
      // 在两种情况下添加分隔区域：
      // 1. 当前项是特殊项，前一项不是特殊项
      // 2. 当前项不是特殊项，前一项是特殊项
      if ((option.isSpecial && i > 0 && !previousWasSpecial) ||
          (!option.isSpecial && i > 0 && previousWasSpecial)) {
        items.add(
          Container(
            height: 8,
            decoration: BoxDecoration(color: Colors.black.withValues(alpha: 0.5)),
          ),
        );
      }

      // 添加菜单项
      items.add(
        InkWell(
          onTap: () {
            option.onTap();
            widget.onClose();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              children: [
                // 标题
                Expanded(
                  child: Text(
                    option.title,
                    style: TextStyle(
                      color:
                          option.customTextColor ??
                          (option.isSelected
                              ? const Color(0xFFFF3B30)
                              : const Color(0xFFE5E5E7)),
                      fontSize: 14,
                      fontWeight:
                          option.isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),

                // 图标移到最右侧，并使用空心样式
                if (option.icon != null)
                  Padding(
                    padding: const EdgeInsets.only(left: 8),
                    child: Icon(
                      option.icon,
                      color:
                          option.customIconColor ??
                          (option.isSelected
                              ? const Color(0xFFFF3B30)
                              : const Color(0xFFE5E5E7)),
                      size: 16,
                    ),
                  ),

                // 选中标记
                if (option.isSelected)
                  const Padding(
                    padding: EdgeInsets.only(left: 6),
                    child: Icon(
                      CupertinoIcons.checkmark_circle,
                      color: Color(0xFFFF3B30),
                      size: 14,
                    ),
                  ),
              ],
            ),
          ),
        ),
      );

      // 添加分隔线，最后一项不添加
      if (i < widget.options.length - 1) {
        final nextIsSpecial =
            (i + 1 < widget.options.length && widget.options[i + 1].isSpecial);

        if (!nextIsSpecial && !option.isSpecial) {
          items.add(
            Divider(
              height: 1,
              thickness: 0.5,
              color: Colors.white.withValues(alpha: 0.08),
              indent: 16,
              endIndent: 16,
            ),
          );
        }
      }

      // 更新前一个项目的状态
      previousWasSpecial = option.isSpecial;
    }

    return items;
  }
}
