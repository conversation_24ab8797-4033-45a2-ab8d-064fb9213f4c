import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart'; // 正确引入QuestionInteractionState
import '../services/auth_service.dart';
import '../core/utils/logger.dart';

/// 填空题组件
/// 用于填空题和计算题
class FillBlankWidget extends StatefulWidget {
  final Question question;
  final String? userAnswer;
  final Function(String?) onAnswerChanged;
  final QuestionInteractionState interactionState;
  final TextInputType keyboardType;

  const FillBlankWidget({
    super.key,
    required this.question,
    this.userAnswer,
    required this.onAnswerChanged,
    required this.interactionState,
    this.keyboardType = TextInputType.text,
  });

  @override
  State<FillBlankWidget> createState() => _FillBlankWidgetState();
}

class _FillBlankWidgetState extends State<FillBlankWidget> {
  late TextEditingController _controller;
  bool _showAnswer = false; // ignore: unused_field
  List<String> _hints = [];
  bool _isLoadingHints = false;
  bool _showHints = false;
  bool _isListening = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.userAnswer);
    _showAnswer =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    
    // 加载智能提示
    _loadSmartHints();
  }

  @override
  void didUpdateWidget(FillBlankWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userAnswer != widget.userAnswer) {
      _controller.text = widget.userAnswer ?? '';
    }

    if (oldWidget.interactionState != widget.interactionState) {
      setState(() {
        _showAnswer =
            widget.interactionState !=
            QuestionInteractionState.waitingForSelection;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 加载智能提示
  Future<void> _loadSmartHints() async {
    if (widget.interactionState != QuestionInteractionState.waitingForSelection) {
      return; // 如果不是等待输入状态，不加载提示
    }

    setState(() {
      _isLoadingHints = true;
    });

    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/smart_hints/fill_blank/${widget.question.id}'),
        headers: {
          'Content-Type': 'application/json',
          // 注意：这里需要从UserProvider获取token，简化处理先注释
          // 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          setState(() {
            _hints = List<String>.from(data['hints'] ?? []);
            _showHints = _hints.isNotEmpty;
          });
        }
      } else {
        // 如果API调用失败，使用默认提示
        _setDefaultHints();
      }
    } catch (e) {
      AppLogger.error('加载智能提示失败', error: e);
      // 使用默认提示
      _setDefaultHints();
    } finally {
      setState(() {
        _isLoadingHints = false;
      });
    }
  }

  /// 设置默认提示词
  void _setDefaultHints() {
    List<String> defaultHints = [];
    
    // 根据题目类型设置不同的默认提示
    if (widget.keyboardType == TextInputType.number) {
      // 计算题的数字提示
      defaultHints = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '-', '×', '÷', '='];
    } else {
      // 填空题的常见答案
      defaultHints = ['是', '不是', '对', '错', '正确', '错误', '有', '没有'];
    }
    
    setState(() {
      _hints = defaultHints;
      _showHints = true;
    });
  }

  /// 处理候选词选择
  void _onHintSelected(String hint) {
    setState(() {
      _controller.text = hint;
    });
    widget.onAnswerChanged(hint);
    
    // 添加触觉反馈
    HapticFeedback.selectionClick();
  }

  /// 开始语音识别（模拟实现）
  void _startVoiceInput() async {
    if (_isListening) return;
    
    setState(() {
      _isListening = true;
    });
    
    try {
      // 添加触觉反馈
      HapticFeedback.mediumImpact();
      
      // 这里是语音识别的模拟实现
      // 实际应用中需要集成语音识别SDK
      await Future.delayed(const Duration(seconds: 2));
      
      // 模拟语音识别结果
      const mockResult = "语音识别结果";
      setState(() {
        _controller.text = mockResult;
      });
      widget.onAnswerChanged(mockResult);
      
    } catch (e) {
      AppLogger.error('语音识别失败', error: e);
    } finally {
      setState(() {
        _isListening = false;
      });
    }
  }

  /// 清空输入
  void _clearInput() {
    setState(() {
      _controller.clear();
    });
    widget.onAnswerChanged('');
    HapticFeedback.selectionClick();
  }

  @override
  Widget build(BuildContext context) {
    // 确定是否显示正确/错误状态
    final bool isCorrect =
        widget.interactionState == QuestionInteractionState.answerCorrect;
    final bool isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final correctAnswer = widget.question.answer ?? ''; // ignore: unused_local_variable

    // 样式颜色
    Color borderColor = Colors.white.withValues(alpha: 0.3);
    if (isShowingFeedback) {
      borderColor = isCorrect ? Colors.green : Colors.red;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 输入框区域
        ClipRRect(
          borderRadius: BorderRadius.circular(24.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(24.0),
                border: Border.all(color: borderColor, width: 2),
              ),
              child: Row(
                children: [
                  // 输入框
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      enabled: !isShowingFeedback,
                      keyboardType: widget.keyboardType,
                      textInputAction: TextInputAction.done,
                      textCapitalization: TextCapitalization.sentences,
                      autocorrect: false,
                      enableSuggestions: false,
                      enableIMEPersonalizedLearning: false,
                      onChanged: (value) {
                        widget.onAnswerChanged(value);
                      },
                      onSubmitted: (value) {
                        widget.onAnswerChanged(value);
                        FocusScope.of(context).unfocus();
                      },
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'Noto Sans SC',
                      ),
                      decoration: InputDecoration(
                        hintText: '输入答案或点击候选词',
                        hintStyle: TextStyle(
                          color: Colors.white.withValues(alpha: 0.5),
                          fontSize: 16,
                          fontFamily: 'Noto Sans SC',
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  
                  // 功能按钮组
                  if (!isShowingFeedback) ...[
                    // 语音输入按钮
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: _startVoiceInput,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _isListening 
                                ? Colors.red.withValues(alpha: 0.3)
                                : Colors.white.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            _isListening ? Icons.mic : Icons.mic_none,
                            color: _isListening ? Colors.red : Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                    
                    // 清空按钮
                    if (_controller.text.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: GestureDetector(
                          onTap: _clearInput,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.clear,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                  ],
                ],
              ),
            ),
          ),
        ),
        
        // 智能候选词区域
        if (_showHints && !isShowingFeedback && _hints.isNotEmpty) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.lightbulb_outline,
                      color: Colors.amber,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    const Text(
                      '智能提示',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showHints = false;
                        });
                      },
                      child: Icon(
                        Icons.close,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _hints.map((hint) {
                    return GestureDetector(
                      onTap: () => _onHintSelected(hint),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.blue.withValues(alpha: 0.3),
                              Colors.purple.withValues(alpha: 0.3),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          hint,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
        
        // 加载提示状态
        if (_isLoadingHints)
          Container(
            margin: const EdgeInsets.only(top: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
            ),
            child: const Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  '正在加载智能提示...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
