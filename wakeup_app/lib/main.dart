import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:wakeup_app/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'providers/user_provider.dart';
import 'providers/api_cache_provider.dart';
import 'providers/locale_provider.dart';
import 'providers/mindmap_provider.dart';
import 'pages/main/main_page.dart';
import 'pages/auth/login_page.dart';
import 'utils/background_loader.dart';
import 'services/auth_service.dart';
import 'constants/fonts.dart'; // 导入字体常量类
import 'core/routing/app_router.dart'; // 导入统一路由管理
import 'core/utils/performance_monitor.dart';
import 'core/routing/lazy_route_manager.dart';
import 'shared/widgets/optimized_image.dart';
import 'core/utils/keyboard_warmup.dart';

// 初始化函数确保应用启动前需要的服务已经准备好
Future<void> initApp() async {
  // 初始化性能监控
  PerformanceMonitor().startMonitoring();

  // 配置图片缓存（对 iPhone XS 更友好的上限）
  ImageMemoryManager.configureImageCache(
    maxCacheImages: 300,
    maxCacheSize: 60 * 1024 * 1024,
  );

  // 初始化认证服务
  await AuthService.initialize();

  // 检查令牌有效性
  final isTokenValid = await AuthService.checkTokenOnStartup();
  if (!isTokenValid) {
    // 如果令牌无效，清除用户数据
    await AuthService.clearToken();
    debugPrint('启动时令牌验证失败，已清除令牌');
  } else {
    debugPrint('令牌有效，保持登录状态');
  }

  // 智能预加载常用路由
  IntelligentPreloader.intelligentPreload();
}

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 执行应用初始化
  await initApp();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => UserProvider()),
        ChangeNotifierProvider(create: (context) => ApiCacheProvider()),
        ChangeNotifierProvider(create: (context) => LocaleProvider()..init()),
        ChangeNotifierProvider(create: (context) => MindMapProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    // 应用首次构建完成后，在后台预加载常用数据
    BackgroundLoader.preloadCommonData(context);

    return CupertinoApp(
      title: 'WakeUP',
      debugShowCheckedModeBanner: false,
      // 添加本地化支持
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        DefaultCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
        Locale('zh'), // Chinese
      ],
      locale: localeProvider.locale,
      localeResolutionCallback: (locale, supportedLocales) {
        // 如果设备语言在支持列表中，使用设备语言
        for (var supportedLocale in supportedLocales) {
          if (locale != null &&
              locale.languageCode == supportedLocale.languageCode) {
            return supportedLocale;
          }
        }
        // 默认使用英文
        return const Locale('en');
      },
      theme: CupertinoThemeData(
        primaryColor: CupertinoColors.white,
        brightness: Brightness.dark,
        scaffoldBackgroundColor: CupertinoColors.black,
        barBackgroundColor: const Color(0xFF111111),
        textTheme: CupertinoTextThemeData(
          navTitleTextStyle: TextStyle(
            fontFamily: AppFonts.platformChineseFont, // 使用平台自适应中文字体
            color: CupertinoColors.white,
            fontSize: 17.0,
            fontWeight: FontWeight.w600,
            fontFamilyFallback: [AppFonts.platformLatinFont], // 添加英文字体回退
          ),
          textStyle: TextStyle(
            fontFamily: AppFonts.platformChineseFont, // 使用平台自适应中文字体
            color: CupertinoColors.white,
            fontFamilyFallback: [AppFonts.platformLatinFont], // 添加英文字体回退
          ),
          actionTextStyle: TextStyle(
            fontFamily: AppFonts.platformChineseFont, // 使用平台自适应中文字体
            color: CupertinoColors.white,
            fontWeight: FontWeight.w600,
            fontFamilyFallback: [AppFonts.platformLatinFont], // 添加英文字体回退
          ),
        ),
      ),
      // 添加Material组件支持
      builder: (context, child) {
        // 首帧后进行一次键盘软预热（仅 iOS）
        WidgetsBinding.instance.addPostFrameCallback((_) {
          KeyboardWarmup.warmUp(context);
        });
        // 提供MaterialLocalizations
        return Material(color: CupertinoColors.black, child: child!);
      },
      home:
          userProvider.isLoading
              ? const CupertinoPageScaffold(
                backgroundColor: CupertinoColors.black,
                child: Center(
                  child: CupertinoActivityIndicator(
                    color: CupertinoColors.white,
                  ),
                ),
              )
              : userProvider.isLoggedIn
              ? const MainPage()
              : const LoginPage(),
      routes: AppRouter.routes,
      onGenerateRoute: (settings) {
        // 兜底：若命名路由未在 routes 中注册，统一回退至登录页
        if (AppRouter.routes.containsKey(settings.name)) return null;
        return CupertinoPageRoute(builder: (_) => const LoginPage());
      },
    );
  }
}
