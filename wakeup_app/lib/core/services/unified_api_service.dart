import '../config/api_endpoints.dart';
import '../utils/logger.dart';
import 'api_client.dart';
import '../../providers/user_provider.dart';
import 'package:provider/provider.dart';
import 'package:flutter/widgets.dart';

/// 统一的API服务层
/// 整合并替代原有的分散服务类
class UnifiedApiService {
  static final UnifiedApiService _instance = UnifiedApiService._internal();
  factory UnifiedApiService() => _instance;
  UnifiedApiService._internal();

  final ApiClient _client = ApiClient();
  final TaggedLogger _logger = const TaggedLogger('UnifiedAPI');

  /// 获取认证token
  String? _getToken(BuildContext? context) {
    if (context != null) {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        return userProvider.token;
      } catch (e) {
        _logger.warning('无法获取用户token: $e');
      }
    }
    return null;
  }

  // ==================== 认证相关 ====================

  /// 用户登录
  Future<ApiResponse<Map<String, dynamic>>> login({
    required String identifier,
    required String password,
    String? verificationCode,
  }) async {
    _logger.network('POST', ApiEndpoints.auth.login);

    return await _client.post(
      ApiEndpoints.auth.login,
      body: {
        'identifier': identifier,
        'password': password,
        if (verificationCode != null) 'verification_code': verificationCode,
      },
    );
  }

  /// 用户注册
  Future<ApiResponse<Map<String, dynamic>>> register({
    required Map<String, dynamic> userData,
  }) async {
    _logger.network('POST', ApiEndpoints.auth.register);

    return await _client.post(ApiEndpoints.auth.register, body: userData);
  }

  /// 发送验证码
  Future<ApiResponse<Map<String, dynamic>>> sendVerificationCode({
    required String phone,
  }) async {
    _logger.network('POST', ApiEndpoints.auth.sendCode);

    return await _client.post(
      ApiEndpoints.auth.sendCode,
      body: {'phone': phone},
    );
  }

  /// 检查账号是否存在
  Future<ApiResponse<Map<String, dynamic>>> checkAccountExists({
    required String account,
    required String accountType,
  }) async {
    _logger.network('POST', ApiEndpoints.auth.checkAccount);

    return await _client.post(
      ApiEndpoints.auth.checkAccount,
      body: {'account': account, 'type': accountType},
    );
  }

  // ==================== 用户相关 ====================

  /// 获取用户资料
  Future<ApiResponse<Map<String, dynamic>>> getUserProfile(
    BuildContext? context,
  ) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.user.profile);

    return await _client.get(ApiEndpoints.user.profile, token: token);
  }

  /// 更新用户资料
  Future<ApiResponse<Map<String, dynamic>>> updateUserProfile({
    required Map<String, dynamic> profileData,
    required BuildContext context,
  }) async {
    final token = _getToken(context);
    _logger.network('POST', ApiEndpoints.user.updateProfile);

    return await _client.post(
      ApiEndpoints.user.updateProfile,
      body: profileData,
      token: token,
    );
  }

  /// 获取用户统计
  Future<ApiResponse<Map<String, dynamic>>> getUserStats(
    BuildContext? context,
  ) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.user.stats);

    return await _client.get(ApiEndpoints.user.stats, token: token);
  }

  // ==================== 课程相关 ====================

  /// 获取课程列表
  Future<ApiResponse<List<dynamic>>> getCourses(BuildContext? context) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.course.list);

    return await _client.get(
      ApiEndpoints.course.list,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  /// 获取用户课程
  Future<ApiResponse<List<dynamic>>> getUserCourses(
    BuildContext? context,
  ) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.course.userCourses);

    return await _client.get(
      ApiEndpoints.course.userCourses,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  /// 获取课程详情
  Future<ApiResponse<Map<String, dynamic>>> getCourseDetail({
    required int courseId,
    BuildContext? context,
  }) async {
    final token = _getToken(context);
    final endpoint = ApiEndpoints.course.courseDetail(courseId);
    _logger.network('GET', endpoint);

    return await _client.get(endpoint, token: token);
  }

  /// 获取课程问题
  Future<ApiResponse<List<dynamic>>> getCourseQuestions({
    required int courseId,
    BuildContext? context,
  }) async {
    final token = _getToken(context);
    final endpoint = ApiEndpoints.course.courseQuestions(courseId);
    _logger.network('GET', endpoint);

    return await _client.get(
      endpoint,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  // ==================== 题目相关 ====================

  /// 获取题目列表
  Future<ApiResponse<List<dynamic>>> getQuestions({
    Map<String, dynamic>? queryParams,
    BuildContext? context,
  }) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.quiz.questions);

    return await _client.get(
      ApiEndpoints.quiz.questions,
      queryParams: queryParams,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  /// 获取题目集
  Future<ApiResponse<List<dynamic>>> getQuestionSet({
    required int setId,
    BuildContext? context,
  }) async {
    final token = _getToken(context);
    final endpoint = ApiEndpoints.quiz.questionSet(setId);
    _logger.network('GET', endpoint);

    return await _client.get(
      endpoint,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  // ==================== 首页相关 ====================

  /// 获取首页头部数据
  Future<ApiResponse<Map<String, dynamic>>> getHomeHeader(
    BuildContext? context,
  ) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.home.header);

    return await _client.get(ApiEndpoints.home.header, token: token);
  }

  /// 获取首页内容数据
  Future<ApiResponse<List<dynamic>>> getHomeContent(
    BuildContext? context,
  ) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.home.content);

    return await _client.get(
      ApiEndpoints.home.content,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  /// 获取推荐内容
  Future<ApiResponse<List<dynamic>>> getRecommendations(
    BuildContext? context,
  ) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.home.recommendations);

    return await _client.get(
      ApiEndpoints.home.recommendations,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  /// 获取活动数据
  Future<ApiResponse<List<dynamic>>> getActivities(
    BuildContext? context,
  ) async {
    final token = _getToken(context);
    _logger.network('GET', ApiEndpoints.home.activities);

    return await _client.get(
      ApiEndpoints.home.activities,
      token: token,
      parser: (data) => data is List ? data : [data],
    );
  }

  // ==================== 通用方法 ====================

  /// 通用GET请求
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    BuildContext? context,
    T Function(dynamic)? parser,
  }) async {
    final token = _getToken(context);
    _logger.network('GET', endpoint);

    return await _client.get(
      endpoint,
      queryParams: queryParams,
      token: token,
      parser: parser,
    );
  }

  /// 通用POST请求
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    BuildContext? context,
    T Function(dynamic)? parser,
  }) async {
    final token = _getToken(context);
    _logger.network('POST', endpoint);

    return await _client.post(
      endpoint,
      body: body,
      token: token,
      parser: parser,
    );
  }

  /// 释放资源
  void dispose() {
    _client.dispose();
  }
}
