import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// 统一的本地存储服务
/// 解决项目中57处分散的SharedPreferences调用
class StorageService {
  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  StorageService._();

  SharedPreferences? _prefs;

  /// 初始化存储服务
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 确保已初始化
  Future<void> _ensureInitialized() async {
    if (_prefs == null) {
      await init();
    }
  }

  // String 操作
  Future<void> setString(String key, String value) async {
    await _ensureInitialized();
    await _prefs!.setString(key, value);
  }

  Future<String?> getString(String key) async {
    await _ensureInitialized();
    return _prefs!.getString(key);
  }

  // Int 操作
  Future<void> setInt(String key, int value) async {
    await _ensureInitialized();
    await _prefs!.setInt(key, value);
  }

  Future<int?> getInt(String key) async {
    await _ensureInitialized();
    return _prefs!.getInt(key);
  }

  // Bool 操作
  Future<void> setBool(String key, bool value) async {
    await _ensureInitialized();
    await _prefs!.setBool(key, value);
  }

  Future<bool?> getBool(String key) async {
    await _ensureInitialized();
    return _prefs!.getBool(key);
  }

  // Double 操作
  Future<void> setDouble(String key, double value) async {
    await _ensureInitialized();
    await _prefs!.setDouble(key, value);
  }

  Future<double?> getDouble(String key) async {
    await _ensureInitialized();
    return _prefs!.getDouble(key);
  }

  // StringList 操作
  Future<void> setStringList(String key, List<String> value) async {
    await _ensureInitialized();
    await _prefs!.setStringList(key, value);
  }

  Future<List<String>?> getStringList(String key) async {
    await _ensureInitialized();
    return _prefs!.getStringList(key);
  }

  // JSON 对象操作
  Future<void> setJson(String key, Map<String, dynamic> value) async {
    await _ensureInitialized();
    final jsonString = json.encode(value);
    await _prefs!.setString(key, jsonString);
  }

  Future<Map<String, dynamic>?> getJson(String key) async {
    await _ensureInitialized();
    final jsonString = _prefs!.getString(key);
    if (jsonString == null) return null;
    try {
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  // 删除操作
  Future<void> remove(String key) async {
    await _ensureInitialized();
    await _prefs!.remove(key);
  }

  // 清除所有数据
  Future<void> clear() async {
    await _ensureInitialized();
    await _prefs!.clear();
  }

  // 检查key是否存在
  Future<bool> containsKey(String key) async {
    await _ensureInitialized();
    return _prefs!.containsKey(key);
  }

  // 获取所有keys
  Future<Set<String>> getKeys() async {
    await _ensureInitialized();
    return _prefs!.getKeys();
  }

  // 常用的业务相关存储操作
  
  /// 存储用户token
  Future<void> setAuthToken(String token) async {
    await setString('auth_token', token);
  }

  /// 获取用户token
  Future<String?> getAuthToken() async {
    return await getString('auth_token');
  }

  /// 删除用户token
  Future<void> clearAuthToken() async {
    await remove('auth_token');
  }

  /// 存储用户信息
  Future<void> setUserInfo(Map<String, dynamic> userInfo) async {
    await setJson('user_info', userInfo);
  }

  /// 获取用户信息
  Future<Map<String, dynamic>?> getUserInfo() async {
    return await getJson('user_info');
  }

  /// 清除用户信息
  Future<void> clearUserInfo() async {
    await remove('user_info');
  }

  /// 存储语言设置
  Future<void> setLocale(String locale) async {
    await setString('app_locale', locale);
  }

  /// 获取语言设置
  Future<String?> getLocale() async {
    return await getString('app_locale');
  }

  /// 存储主题设置
  Future<void> setTheme(String theme) async {
    await setString('app_theme', theme);
  }

  /// 获取主题设置
  Future<String?> getTheme() async {
    return await getString('app_theme');
  }

  /// 存储学习进度
  Future<void> setLearningProgress(int courseId, Map<String, dynamic> progress) async {
    await setJson('learning_progress_$courseId', progress);
  }

  /// 获取学习进度
  Future<Map<String, dynamic>?> getLearningProgress(int courseId) async {
    return await getJson('learning_progress_$courseId');
  }

  /// 存储答题记录
  Future<void> setQuizRecord(String recordKey, Map<String, dynamic> record) async {
    await setJson('quiz_record_$recordKey', record);
  }

  /// 获取答题记录
  Future<Map<String, dynamic>?> getQuizRecord(String recordKey) async {
    return await getJson('quiz_record_$recordKey');
  }
}