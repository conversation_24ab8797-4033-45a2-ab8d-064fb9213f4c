/// 性能优化配置
class PerformanceConfig {
  // 图片缓存配置
  static const int maxImageCacheSize = 100 * 1024 * 1024; // 100MB
  static const int maxImageCacheCount = 1000;

  // 列表优化配置
  static const int listItemBufferSize = 5;
  static const double listEndReachedThreshold = 200.0;
  static const int maxListCacheSize = 500;

  // 网络请求配置
  static const Duration networkTimeout = Duration(seconds: 15);
  static const int maxConcurrentRequests = 5;
  static const Duration requestDebounceDelay = Duration(milliseconds: 300);

  // 缓存配置
  static const Duration defaultCacheExpiry = Duration(minutes: 10);
  static const int maxCacheEntries = 50;
  static const Duration cacheCleanupInterval = Duration(minutes: 5);

  // 状态管理配置
  static const Duration stateDebounceDelay = Duration(milliseconds: 100);
  static const int maxStateHistorySize = 20;

  // 预加载配置
  static const int highPriorityThreshold = 8;
  static const int mediumPriorityThreshold = 5;
  static const Duration preloadDelay = Duration(milliseconds: 100);
  static const Duration lowPriorityPreloadDelay = Duration(milliseconds: 500);

  // 性能监控配置（调整为更宽松的阈值）
  static const Duration memoryMonitorInterval = Duration(seconds: 30);
  static const Duration performanceCleanupInterval = Duration(minutes: 30);
  static const double fpsAlertThreshold = 20.0; // 降低到20 FPS避免误报
  static const double frameTimeAlertThreshold = 50.0; // 提高到50ms
  static const double memoryAlertThreshold = 300.0; // 提高到300MB

  // 路由优化配置
  static const int maxCachedRoutes = 10;
  static const Duration routeCacheExpiry = Duration(days: 7);
  static const int routeAccessCountThreshold = 3;

  // 开发模式配置
  static const bool enablePerformanceDebugging = false; // 暂时禁用减少干扰
  static const bool enableMemoryDebugging = false;
  static const bool enableNetworkDebugging = false;
}
