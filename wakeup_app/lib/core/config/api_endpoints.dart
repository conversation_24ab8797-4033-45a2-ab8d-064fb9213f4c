/// API 端点统一管理
class ApiEndpoints {
  // 认证相关
  static const auth = AuthEndpoints();

  // 用户相关
  static const user = UserEndpoints();

  // 课程相关
  static const course = CourseEndpoints();

  // 题目相关
  static const quiz = QuizEndpoints();

  // 首页相关
  static const home = HomeEndpoints();

  // AI相关
  static const ai = AiEndpoints();
}

class AuthEndpoints {
  const AuthEndpoints();

  String get login => '/api/auth/login';
  String get register => '/api/auth/register';
  String get sendCode => '/api/auth/send_code';
  String get checkAccount => '/api/auth/check_account';
  String get profile => '/api/profile';
  String get verify => '/api/auth/verify';
}

class UserEndpoints {
  const UserEndpoints();

  String get profile => '/api/profile';
  String get updateProfile => '/api/user/update';
  String get stats => '/api/user/stats';
  String get favorites => '/api/user/favorites';
}

class CourseEndpoints {
  const CourseEndpoints();

  String get list => '/api/courses';
  String get userCourses => '/api/user/courses';
  String courseDetail(int id) => '/api/courses/$id';
  String courseQuestions(int id) => '/api/courses/$id/questions';
  String courseTabs(int id) => '/api/courses/$id/tabs';
}

class QuizEndpoints {
  const QuizEndpoints();

  String get questions => '/api/questions';
  String get categories => '/api/categories';
  String questionSet(int id) => '/api/quiz/questions/set/$id';
  String questionsWithLevel4(int level4Id) =>
      '/api/questions?level4_id=$level4Id';
  String attempt(int id) => '/api/quiz/attempt/$id';
}

class HomeEndpoints {
  const HomeEndpoints();

  String get header => '/api/home/<USER>';
  String get content => '/api/home/<USER>';
  String get recommendations => '/api/recommendations';
  String get activities => '/api/activities';
}

class AiEndpoints {
  const AiEndpoints();

  String get branches => '/api/ai/branches';
  String get ask => '/api/ai/ask';
}

/// API 缓存键管理
class ApiCacheKeys {
  static String profile = 'api_cache_/api/profile';
  static String courses = 'api_cache_/api/courses';
  static String userCourses = 'api_cache_/api/user/courses';
  static String homeHeader = 'api_cache_/api/home/<USER>';
  static String homeContent = 'api_cache_/api/home/<USER>';
  static String recommendations = 'api_cache_/api/recommendations';
  static String activities = 'api_cache_/api/activities';

  static String courseDetail(int id) => 'api_cache_/api/courses/$id';
  static String courseQuestions(int id) =>
      'api_cache_/api/courses/$id/questions';
  static String questionSet(int id) => 'api_cache_/api/quiz/questions/set/$id';
}
