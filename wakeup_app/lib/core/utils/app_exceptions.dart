/// 应用程序异常基类
abstract class AppException implements Exception {
  final String message;
  final int? statusCode;

  const AppException(this.message, [this.statusCode]);

  @override
  String toString() => message;
}

/// API请求异常
class ApiException extends AppException {
  const ApiException(super.message, [super.statusCode]);
}

/// 网络连接异常
class NetworkException extends AppException {
  const NetworkException(super.message);
}

/// 请求超时异常
class TimeoutException extends AppException {
  const TimeoutException(super.message);
}

/// 认证异常
class AuthException extends AppException {
  const AuthException(super.message, [super.statusCode]);
}

/// 未知异常
class UnknownException extends AppException {
  const UnknownException(super.message);
}

/// 数据解析异常
class ParseException extends AppException {
  const ParseException(super.message);
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException(super.message);
}