import 'package:flutter/foundation.dart';

/// 统一日志管理器
class AppLogger {
  static const String _tag = 'WakeUp';
  static bool _isDebugMode = kDebugMode;

  /// 设置调试模式
  static void setDebugMode(bool enabled) {
    _isDebugMode = enabled;
  }

  /// 信息日志
  static void info(String message, {String? tag}) {
    if (_isDebugMode) {
      debugPrint('ℹ️ [${tag ?? _tag}] $message');
    }
  }

  /// 警告日志
  static void warning(String message, {String? tag}) {
    if (_isDebugMode) {
      debugPrint('⚠️ [${tag ?? _tag}] $message');
    }
  }

  /// 错误日志
  static void error(String message, {Object? error, String? tag}) {
    if (_isDebugMode) {
      debugPrint('❌ [${tag ?? _tag}] $message');
      if (error != null) {
        debugPrint('   Error: $error');
      }
    }
  }

  /// 成功日志
  static void success(String message, {String? tag}) {
    if (_isDebugMode) {
      debugPrint('✅ [${tag ?? _tag}] $message');
    }
  }

  /// 网络请求日志
  static void network(
    String method,
    String url, {
    int? statusCode,
    String? tag,
  }) {
    if (_isDebugMode) {
      final status = statusCode != null ? ' ($statusCode)' : '';
      debugPrint('🌐 [${tag ?? 'Network'}] $method $url$status');
    }
  }

  /// 缓存日志
  static void cache(String action, String key, {String? tag}) {
    if (_isDebugMode) {
      debugPrint('💾 [${tag ?? 'Cache'}] $action: $key');
    }
  }

  /// 性能日志
  static void performance(String operation, int milliseconds, {String? tag}) {
    if (_isDebugMode) {
      final icon = milliseconds > 100 ? '🐌' : '⚡';
      debugPrint(
        '$icon [${tag ?? 'Performance'}] $operation: ${milliseconds}ms',
      );
    }
  }

  /// 预加载日志
  static void preload(String message, {String? tag}) {
    if (_isDebugMode) {
      debugPrint('🚀 [${tag ?? 'Preload'}] $message');
    }
  }
}

/// 带标签的日志记录器
class TaggedLogger {
  final String tag;

  const TaggedLogger(this.tag);

  void info(String message) => AppLogger.info(message, tag: tag);
  void warning(String message) => AppLogger.warning(message, tag: tag);
  void error(String message, {Object? error}) =>
      AppLogger.error(message, error: error, tag: tag);
  void success(String message) => AppLogger.success(message, tag: tag);
  void network(String method, String url, {int? statusCode}) =>
      AppLogger.network(method, url, statusCode: statusCode, tag: tag);
  void cache(String action, String key) =>
      AppLogger.cache(action, key, tag: tag);
  void performance(String operation, int milliseconds) =>
      AppLogger.performance(operation, milliseconds, tag: tag);
}
