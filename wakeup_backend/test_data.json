{"users": [{"id": 888, "nickname": "测试用户", "email": "<EMAIL>", "oauth_provider": "test", "oauth_open_id": "test_888", "token": "temp_skip_token_for_testing", "oauth_avatar_url": null, "phone": null, "gender": null, "region": "中国", "country_code": "CN", "username": null, "password_hash": null}], "regions": [{"id": 1, "name": "中国", "english_name": "China", "code": "CN", "is_active": true}, {"id": 2, "name": "美国", "english_name": "United States", "code": "US", "is_active": true}, {"id": 3, "name": "英国", "english_name": "United Kingdom", "code": "UK", "is_active": true}], "category_level1": [{"id": 1, "name": "计算机科学", "english_name": "Computer Science", "is_active": true}, {"id": 2, "name": "数据科学", "english_name": "Data Science", "is_active": true}, {"id": 3, "name": "Web开发", "english_name": "Web Development", "is_active": true}, {"id": 4, "name": "移动开发", "english_name": "Mobile Development", "is_active": true}, {"id": 5, "name": "语言学习", "english_name": "Language Learning", "is_active": true}], "category_level2": [{"id": 1, "name": "算法与数据结构", "english_name": "Algorithms & Data Structures", "description": "基础算法和数据结构", "level1_id": 1, "is_active": true}, {"id": 2, "name": "数据库系统", "english_name": "Database Systems", "description": "关系型和非关系型数据库", "level1_id": 1, "is_active": true}, {"id": 3, "name": "机器学习", "english_name": "Machine Learning", "description": "机器学习算法和应用", "level1_id": 2, "is_active": true}, {"id": 4, "name": "数据分析", "english_name": "Data Analysis", "description": "数据处理和可视化", "level1_id": 2, "is_active": true}, {"id": 5, "name": "前端开发", "english_name": "Frontend Development", "description": "HTML, CSS, JavaScript等前端技术", "level1_id": 3, "is_active": true}, {"id": 6, "name": "后端开发", "english_name": "Backend Development", "description": "服务器端开发技术", "level1_id": 3, "is_active": true}, {"id": 7, "name": "iOS开发", "english_name": "iOS Development", "description": "iPhone和iPad应用开发", "level1_id": 4, "is_active": true}, {"id": 8, "name": "Android开发", "english_name": "Android Development", "description": "Android应用开发", "level1_id": 4, "is_active": true}, {"id": 9, "name": "英语", "english_name": "English", "description": "英语学习课程", "level1_id": 5, "is_active": true}, {"id": 10, "name": "中文", "english_name": "Chinese", "description": "中文学习课程", "level1_id": 5, "is_active": true}], "courses": [{"id": 1, "name": "Python基础入门", "english_name": "Python Fundamentals", "description": "从零开始学习Python编程语言，掌握基础语法和编程概念", "category_level2_id": 1, "difficulty_level": "beginner", "duration_hours": 40, "is_active": true, "display_order": 1, "cover_image": "https://example.com/python-basics.jpg"}, {"id": 2, "name": "JavaScript高级教程", "english_name": "Advanced JavaScript", "description": "深入学习JavaScript的高级特性和ES6+新功能", "category_level2_id": 5, "difficulty_level": "intermediate", "duration_hours": 60, "is_active": true, "display_order": 1, "cover_image": "https://example.com/javascript-advanced.jpg"}, {"id": 3, "name": "React前端开发", "english_name": "React Frontend Development", "description": "使用React构建现代化的Web应用程序", "category_level2_id": 5, "difficulty_level": "intermediate", "duration_hours": 80, "is_active": true, "display_order": 2, "cover_image": "https://example.com/react-development.jpg"}, {"id": 4, "name": "Node.js后端开发", "english_name": "Node.js Backend Development", "description": "使用Node.js构建高性能的服务器端应用", "category_level2_id": 6, "difficulty_level": "intermediate", "duration_hours": 70, "is_active": true, "display_order": 1, "cover_image": "https://example.com/nodejs-backend.jpg"}, {"id": 5, "name": "Swift iOS开发", "english_name": "Swift iOS Development", "description": "使用Swift语言开发iPhone和iPad应用", "category_level2_id": 7, "difficulty_level": "intermediate", "duration_hours": 90, "is_active": true, "display_order": 1, "cover_image": "https://example.com/swift-ios.jpg"}, {"id": 6, "name": "Flutter跨平台开发", "english_name": "Flutter Cross-Platform Development", "description": "使用Flutter开发iOS和Android应用", "category_level2_id": 8, "difficulty_level": "advanced", "duration_hours": 100, "is_active": true, "display_order": 1, "cover_image": "https://example.com/flutter-development.jpg"}, {"id": 7, "name": "机器学习基础", "english_name": "Machine Learning Fundamentals", "description": "学习机器学习的基本概念和常用算法", "category_level2_id": 3, "difficulty_level": "intermediate", "duration_hours": 120, "is_active": true, "display_order": 1, "cover_image": "https://example.com/machine-learning.jpg"}, {"id": 8, "name": "数据分析与可视化", "english_name": "Data Analysis & Visualization", "description": "使用Python进行数据分析和可视化", "category_level2_id": 4, "difficulty_level": "intermediate", "duration_hours": 80, "is_active": true, "display_order": 1, "cover_image": "https://example.com/data-analysis.jpg"}, {"id": 9, "name": "SQL数据库设计", "english_name": "SQL Database Design", "description": "学习关系型数据库设计和SQL查询优化", "category_level2_id": 2, "difficulty_level": "intermediate", "duration_hours": 50, "is_active": true, "display_order": 1, "cover_image": "https://example.com/sql-database.jpg"}, {"id": 10, "name": "英语口语提升", "english_name": "English Speaking Improvement", "description": "提升英语口语表达能力和发音准确性", "category_level2_id": 9, "difficulty_level": "beginner", "duration_hours": 60, "is_active": true, "display_order": 1, "cover_image": "https://example.com/english-speaking.jpg"}], "user_courses": [{"id": 1, "user_id": 888, "course_id": 1, "course_name": "Python基础入门", "description": "从零开始学习Python编程语言", "status": "in_progress", "progress_percentage": 30, "study_hours": 12, "correct_answers": 25, "total_answers": 35, "last_studied_at": "2024-01-15T10:30:00", "started_at": "2024-01-01T00:00:00"}, {"id": 2, "user_id": 888, "course_id": 2, "course_name": "JavaScript高级教程", "description": "深入学习JavaScript的高级特性", "status": "in_progress", "progress_percentage": 60, "study_hours": 24, "correct_answers": 45, "total_answers": 55, "last_studied_at": "2024-01-20T14:15:00", "started_at": "2024-01-05T00:00:00"}, {"id": 3, "user_id": 888, "course_id": 3, "course_name": "React前端开发", "description": "使用React构建现代化Web应用", "status": "in_progress", "progress_percentage": 15, "study_hours": 6, "correct_answers": 10, "total_answers": 18, "last_studied_at": "2024-01-18T09:45:00", "started_at": "2024-01-10T00:00:00"}, {"id": 4, "user_id": 888, "course_id": 7, "course_name": "机器学习基础", "description": "学习机器学习的基本概念和算法", "status": "completed", "progress_percentage": 100, "study_hours": 48, "correct_answers": 85, "total_answers": 90, "last_studied_at": "2024-01-12T16:30:00", "started_at": "2023-12-20T00:00:00", "completed_at": "2024-01-12T16:30:00"}, {"id": 5, "user_id": 888, "course_id": 10, "course_name": "英语口语提升", "description": "提升英语口语表达能力", "status": "in_progress", "progress_percentage": 80, "study_hours": 32, "correct_answers": 65, "total_answers": 75, "last_studied_at": "2024-01-22T11:20:00", "started_at": "2024-01-08T00:00:00"}]}