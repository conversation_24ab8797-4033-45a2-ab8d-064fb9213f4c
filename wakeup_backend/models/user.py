from . import db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

class User(db.Model):
    __tablename__ = 'user'
    id = db.Column(db.Integer, primary_key=True)
    
    # 基础信息 - 仅保留必要字段，支持纯OAuth登录
    email = db.Column(db.String(100), unique=True, nullable=True)  # 邮箱（来自OAuth或用户补充）
    nickname = db.Column(db.String(50), nullable=False)  # 昵称（OAuth获取或自动生成）
    
    # 可选个人信息
    phone = db.Column(db.String(20), unique=True, nullable=True)  # 手机号（用户可选补充）
    gender = db.Column(db.String(10), nullable=True)  # 性别
    region = db.Column(db.String(50), nullable=True)  # 地区
    country_code = db.Column(db.String(10), nullable=True)  # 国家代码
    
    # OAuth 2.0 社交登录字段（核心字段）
    oauth_provider = db.Column(db.String(20), nullable=False)  # 登录提供商: apple, wechat, qq, google
    oauth_open_id = db.Column(db.String(100), nullable=False)  # 第三方平台唯一ID
    oauth_union_id = db.Column(db.String(100), nullable=True)  # 第三方平台联合ID（微信专用）
    oauth_access_token = db.Column(db.Text, nullable=True)  # 访问令牌
    oauth_refresh_token = db.Column(db.Text, nullable=True)  # 刷新令牌
    oauth_avatar_url = db.Column(db.String(500), nullable=True)  # 头像URL
    
    # 应用令牌（用于app内认证）
    token = db.Column(db.String(128), nullable=True)  # 登录令牌
    
    # 废弃字段（保留兼容性，后续可删除）
    username = db.Column(db.String(15), unique=True, nullable=True)  # 已废弃
    password_hash = db.Column(db.String(128), nullable=True)  # 已废弃
    code = db.Column(db.String(6), nullable=True)  # 已废弃
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        if not self.password_hash:
            return False
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'phone': self.phone,
            'email': self.email,
            'nickname': self.nickname,
            'gender': self.gender,
            'region': self.region,
            'country_code': self.country_code,
            'oauth_provider': self.oauth_provider,
            'oauth_open_id': self.oauth_open_id,
            'oauth_union_id': self.oauth_union_id,
            'oauth_avatar_url': self.oauth_avatar_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
