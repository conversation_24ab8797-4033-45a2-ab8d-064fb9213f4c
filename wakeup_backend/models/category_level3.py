# models/category_level3.py

from . import db
from .category_level2 import CategoryLevel2

class CategoryLevel3(db.Model):
    __tablename__ = 'category_level3'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    english_name = db.Column(db.String(255))

    level2_id = db.Column(db.In<PERSON>ger, db.ForeignKey('category_level2.id'))
    level2 = db.relationship("CategoryLevel2", backref="level3_list")

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "level2_id": self.level2_id,
            "is_active": self.is_active,
            "english_name": self.english_name
        }
