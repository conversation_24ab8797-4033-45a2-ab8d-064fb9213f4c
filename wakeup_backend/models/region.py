from . import db
from datetime import datetime

class Region(db.Model):
    """国家地区模型"""
    __tablename__ = 'regions'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(10), unique=True, nullable=False)  # 地区代码，如 'CN', 'US', 'JP'
    name = db.Column(db.String(100), nullable=False)  # 地区名称
    english_name = db.Column(db.String(100), nullable=False)  # 英文名称
    is_active = db.Column(db.<PERSON><PERSON>, default=True)  # 是否启用
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'english_name': self.english_name,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        } 