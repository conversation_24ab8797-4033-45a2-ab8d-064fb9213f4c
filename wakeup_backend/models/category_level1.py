from . import db

class CategoryLevel1(db.Model):
    __tablename__ = 'category_level1'

    id = db.<PERSON>umn(db.<PERSON><PERSON>, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    english_name = db.Column(db.String(255))
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "is_active": self.is_active,
            "english_name": self.english_name
        }