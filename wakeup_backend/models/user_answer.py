from . import db
from sqlalchemy.dialects.sqlite import JSON

class UserAnswer(db.Model):
    __tablename__ = "user_answers"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>("user.id"), nullable=False)
    question_id = db.Column(db.Integer, db.<PERSON>ey("questions.id"), nullable=False)
    user_answer = db.Column(JSON)  # 用户的答案
    is_correct = db.Column(db.<PERSON>)
    answer_time = db.Column(db.Float)  # 答题时间（秒）
    attempt_count = db.Column(db.Integer, default=1)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # 关联关系
    user = db.relationship("User", backref="user_answers")
    question = db.relationship("Question", backref="user_answers")

    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "question_id": self.question_id,
            "user_answer": self.user_answer,
            "is_correct": self.is_correct,
            "answer_time": self.answer_time,
            "attempt_count": self.attempt_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
