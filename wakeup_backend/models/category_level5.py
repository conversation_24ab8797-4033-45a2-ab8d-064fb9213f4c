from . import db
from .category_level4 import CategoryLevel4
from sqlalchemy.orm import relationship

# 创建真正的CategoryLevel5类
class CategoryLevel5(db.Model):
    __tablename__ = 'category_level5'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.<PERSON>, default=True)
    english_name = db.Column(db.String(255))  # 添加英文名称字段
    
    # 设置与四级分类的关系
    level4_id = db.Column(db.Integer, db.<PERSON>Key('category_level4.id'))
    level4 = relationship("CategoryLevel4", backref="level5_categories")
    
    # 标签类型，用于前端标签页显示
    tab_type = db.Column(db.String(50), default='quiz')  # 'quiz', 'resource', 'article', 等

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "level4_id": self.level4_id,
            "is_active": self.is_active,
            "tab_type": self.tab_type,
            "english_name": self.english_name
        }
    
    # 获取该五级分类下对应类型的六级内容
    def get_level6_items(self):
        if self.tab_type == 'quiz':
            return QuestionSet.query.filter_by(category_level5_id=self.id).all()
        # 未来可以添加其他类型的六级内容获取逻辑
        return []
