from . import db

class Question(db.Model):
    __tablename__ = "questions"

    id = db.Column(db.Integer, primary_key=True)
    question_text = db.Column(db.Text, nullable=False)
    question_type = db.Column(db.String(50), nullable=False)
    options = db.Column(db.Text)  # 存储选项的JSON字符串
    answer = db.Column(db.Text)   # 存储答案的JSON字符串
    explanation = db.Column(db.Text)
    difficulty = db.Column(db.String(20))
    category_level4_id = db.Column(db.Integer, db.ForeignKey("category_level4.id"))
    
    # 智能提示相关字段
    hints = db.Column(db.Text)  # 存储提示词的JSON字符串，用于智能候选
    templates = db.Column(db.Text)  # 存储答题模板的JSON字符串，用于简答题等
    auto_complete = db.Column(db.Text)  # 存储自动补全数据的JSON字符串
    code_snippets = db.Column(db.Text)  # 存储代码片段的JSON字符串，用于编程题
    matching_hints = db.Column(db.Text)  # 存储匹配提示的JSON字符串，用于匹配题
    ordering_hints = db.Column(db.Text)  # 存储排序提示的JSON字符串，用于排序题
    
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())
    is_active = db.Column(db.Boolean, default=True)

    # 关联关系
    category_level4 = db.relationship("CategoryLevel4", backref="questions")

    def to_dict(self):
        import json
        
        # 处理JSON字段的通用函数
        def parse_json_field(field_value):
            if isinstance(field_value, str):
                try:
                    return json.loads(field_value)
                except (json.JSONDecodeError, TypeError):
                    return None
            return field_value
        
        # 处理options字段：如果是字符串则尝试解析，否则直接使用
        options = parse_json_field(self.options) or []
        
        # 处理answer字段：如果是字符串则尝试解析，否则直接使用
        answer = parse_json_field(self.answer)
        
        # 处理智能提示相关字段
        hints = parse_json_field(self.hints) or []
        templates = parse_json_field(self.templates) or []
        auto_complete = parse_json_field(self.auto_complete) or []
        code_snippets = parse_json_field(self.code_snippets) or []
        matching_hints = parse_json_field(self.matching_hints) or []
        ordering_hints = parse_json_field(self.ordering_hints) or []
        
        return {
            "id": self.id,
            "question_text": self.question_text,
            "question_type": self.question_type,
            "options": options,
            "answer": answer,
            "explanation": self.explanation,
            "difficulty": self.difficulty,
            "category_level4_id": self.category_level4_id,
            
            # 智能提示相关字段
            "hints": hints,
            "templates": templates,
            "auto_complete": auto_complete,
            "code_snippets": code_snippets,
            "matching_hints": matching_hints,
            "ordering_hints": ordering_hints,
            
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_active": self.is_active
        }
