"""
智能提示服务
提供各种题型的智能提示功能，包括候选词、模板、代码片段等
"""
import json
import re
from typing import List, Dict, Any, Optional
from models.question import Question


class SmartHintService:
    """智能提示服务类"""

    @staticmethod
    def get_fill_blank_hints(question: Question, user_input: str = "") -> List[str]:
        """
        获取填空题的智能提示
        
        Args:
            question: 题目对象
            user_input: 用户当前输入的内容
            
        Returns:
            候选词列表
        """
        hints = []
        
        # 从数据库获取预设提示
        if question.hints:
            try:
                db_hints = json.loads(question.hints) if isinstance(question.hints, str) else question.hints
                hints.extend(db_hints)
            except (json.JSONDecodeError, TypeError):
                pass
        
        # 基于题目内容生成智能提示
        content_hints = SmartHintService._generate_content_based_hints(question.question_text, question.question_type)
        hints.extend(content_hints)
        
        # 基于用户输入过滤和排序
        if user_input:
            filtered_hints = [hint for hint in hints if user_input.lower() in hint.lower()]
            hints = filtered_hints + [hint for hint in hints if hint not in filtered_hints]
        
        # 去重并限制数量
        unique_hints = list(dict.fromkeys(hints))[:8]
        return unique_hints

    @staticmethod
    def get_short_answer_templates(question: Question) -> List[Dict[str, str]]:
        """
        获取简答题的回答模板
        
        Args:
            question: 题目对象
            
        Returns:
            模板列表，每个模板包含 'label' 和 'template' 字段
        """
        templates = []
        
        # 从数据库获取预设模板
        if question.templates:
            try:
                db_templates = json.loads(question.templates) if isinstance(question.templates, str) else question.templates
                templates.extend(db_templates)
            except (json.JSONDecodeError, TypeError):
                pass
        
        # 默认通用模板
        default_templates = [
            {"label": "因果关系", "template": "因为...，所以..."},
            {"label": "定义解释", "template": "...是指...，它的特点是..."},
            {"label": "步骤说明", "template": "首先...，然后...，最后..."},
            {"label": "优缺点", "template": "优点：...；缺点：..."},
            {"label": "总结归纳", "template": "综上所述，..."}
        ]
        
        # 如果没有预设模板，使用默认模板
        if not templates:
            templates = default_templates
        
        return templates[:6]  # 限制数量

    @staticmethod
    def get_programming_snippets(question: Question, language: str = "python") -> List[Dict[str, str]]:
        """
        获取编程题的代码片段
        
        Args:
            question: 题目对象
            language: 编程语言
            
        Returns:
            代码片段列表，每个片段包含 'label' 和 'code' 字段
        """
        snippets = []
        
        # 从数据库获取预设代码片段
        if question.code_snippets:
            try:
                db_snippets = json.loads(question.code_snippets) if isinstance(question.code_snippets, str) else question.code_snippets
                snippets.extend(db_snippets)
            except (json.JSONDecodeError, TypeError):
                pass
        
        # 根据编程语言提供默认代码片段
        if language.lower() == "python":
            default_snippets = [
                {"label": "for循环", "code": "for i in range(n):\n    pass"},
                {"label": "if条件", "code": "if condition:\n    pass\nelse:\n    pass"},
                {"label": "函数定义", "code": "def function_name(param):\n    return result"},
                {"label": "列表推导", "code": "[expr for item in iterable if condition]"},
                {"label": "异常处理", "code": "try:\n    pass\nexcept Exception as e:\n    pass"},
                {"label": "字典操作", "code": "dict.get(key, default_value)"},
                {"label": "输入输出", "code": "print(\"Hello World\")\ninput(\"Enter: \")"}
            ]
        elif language.lower() == "javascript":
            default_snippets = [
                {"label": "for循环", "code": "for (let i = 0; i < n; i++) {\n    // code\n}"},
                {"label": "if条件", "code": "if (condition) {\n    // code\n} else {\n    // code\n}"},
                {"label": "函数定义", "code": "function functionName(param) {\n    return result;\n}"},
                {"label": "箭头函数", "code": "const funcName = (param) => {\n    return result;\n}"},
                {"label": "异步函数", "code": "async function funcName() {\n    await promise;\n}"},
                {"label": "数组方法", "code": "array.map(item => item * 2)"},
                {"label": "控制台输出", "code": "console.log(\"Hello World\");"}
            ]
        else:
            default_snippets = []
        
        # 如果没有预设片段，使用默认片段
        if not snippets:
            snippets = default_snippets
        
        return snippets[:8]  # 限制数量

    @staticmethod
    def get_matching_suggestions(question: Question) -> List[Dict[str, Any]]:
        """
        获取匹配题的智能匹配建议
        
        Args:
            question: 题目对象
            
        Returns:
            匹配建议列表
        """
        suggestions = []
        
        # 从数据库获取匹配提示
        if question.matching_hints:
            try:
                db_hints = json.loads(question.matching_hints) if isinstance(question.matching_hints, str) else question.matching_hints
                suggestions.extend(db_hints)
            except (json.JSONDecodeError, TypeError):
                pass
        
        # 基于匹配项内容生成智能建议
        if question.matchingItems:
            # 这里可以实现基于内容相似度的匹配建议
            # 简化实现：提供一键全部匹配的建议
            auto_match = {
                "type": "auto_match",
                "label": "智能匹配",
                "description": "基于内容相似度自动匹配"
            }
            suggestions.append(auto_match)
        
        return suggestions

    @staticmethod
    def get_ordering_suggestions(question: Question) -> List[Dict[str, Any]]:
        """
        获取排序题的智能排序建议
        
        Args:
            question: 题目对象
            
        Returns:
            排序建议列表
        """
        suggestions = []
        
        # 从数据库获取排序提示
        if question.ordering_hints:
            try:
                db_hints = json.loads(question.ordering_hints) if isinstance(question.ordering_hints, str) else question.ordering_hints
                suggestions.extend(db_hints)
            except (json.JSONDecodeError, TypeError):
                pass
        
        # 基于排序项内容生成智能建议
        if question.orderingItems:
            # 检测排序类型并提供相应建议
            items_text = [item.content for item in question.orderingItems]
            
            # 检测是否为数字排序
            if SmartHintService._is_numeric_sequence(items_text):
                suggestions.append({
                    "type": "numeric_sort",
                    "label": "数值排序",
                    "description": "按数值大小排序"
                })
            
            # 检测是否为字母排序
            if SmartHintService._is_alphabetic_sequence(items_text):
                suggestions.append({
                    "type": "alphabetic_sort",
                    "label": "字母排序",
                    "description": "按字母顺序排序"
                })
            
            # 检测是否为时间排序
            if SmartHintService._is_time_sequence(items_text):
                suggestions.append({
                    "type": "time_sort",
                    "label": "时间排序",
                    "description": "按时间先后排序"
                })
            
            # 提供逆序选项
            suggestions.append({
                "type": "reverse_sort",
                "label": "逆序排列",
                "description": "将当前顺序反转"
            })
        
        return suggestions

    @staticmethod
    def get_calculation_hints(question: Question, user_input: str = "") -> List[str]:
        """
        获取计算题的提示
        
        Args:
            question: 题目对象
            user_input: 用户当前输入
            
        Returns:
            计算提示列表
        """
        hints = []
        
        # 数字快捷按钮
        number_hints = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]
        
        # 运算符提示
        operator_hints = ["+", "-", "×", "÷", "=", "(", ")", ".", "%"]
        
        # 常用数学常数
        constant_hints = ["π", "e", "√"]
        
        hints.extend(number_hints)
        hints.extend(operator_hints)
        hints.extend(constant_hints)
        
        return hints

    @staticmethod
    def _generate_content_based_hints(question_text: str, question_type: str) -> List[str]:
        """
        基于题目内容生成提示
        
        Args:
            question_text: 题目文本
            question_type: 题目类型
            
        Returns:
            生成的提示列表
        """
        hints = []
        
        # 提取题目中的关键词作为提示
        # 移除标点符号，获取关键词
        words = re.findall(r'\b\w+\b', question_text.lower())
        
        # 基于题目类型添加相关提示
        if question_type == "fill_blank":
            # 填空题常见答案
            common_answers = ["是", "不是", "对", "错", "正确", "错误", "有", "没有"]
            hints.extend(common_answers)
        
        # 从题目文本中提取可能的答案词汇
        # 这里可以实现更复杂的NLP算法
        extracted_words = [word for word in words if len(word) > 1][:5]
        hints.extend(extracted_words)
        
        return hints

    @staticmethod
    def _is_numeric_sequence(items: List[str]) -> bool:
        """检测是否为数字序列"""
        try:
            numbers = [float(re.search(r'-?\d+\.?\d*', item).group()) for item in items if re.search(r'-?\d+\.?\d*', item)]
            return len(numbers) >= len(items) * 0.7  # 70%以上包含数字
        except:
            return False

    @staticmethod
    def _is_alphabetic_sequence(items: List[str]) -> bool:
        """检测是否为字母序列"""
        return all(item.strip() and item.strip()[0].isalpha() for item in items)

    @staticmethod
    def _is_time_sequence(items: List[str]) -> bool:
        """检测是否为时间序列"""
        time_patterns = [
            r'\d{4}年',  # 2023年
            r'\d{1,2}月',  # 1月, 12月
            r'\d{1,2}日',  # 1日, 31日
            r'\d{1,2}:\d{2}',  # 13:30
            r'第\d+',  # 第一, 第二
        ]
        
        for item in items:
            if any(re.search(pattern, item) for pattern in time_patterns):
                return True
        return False

    @staticmethod
    def generate_smart_hints_for_question(question_id: int, user_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        为指定题目生成智能提示
        
        Args:
            question_id: 题目ID
            user_context: 用户上下文信息（可选）
            
        Returns:
            包含各种智能提示的字典
        """
        # 这里应该从数据库获取题目，为了简化，我们先返回模拟数据
        # 实际实现时需要查询数据库
        
        result = {
            "question_id": question_id,
            "fill_blank_hints": [],
            "short_answer_templates": [],
            "programming_snippets": [],
            "matching_suggestions": [],
            "ordering_suggestions": [],
            "calculation_hints": []
        }
        
        # 实际实现时，这里应该：
        # 1. 从数据库查询题目
        # 2. 根据题目类型调用相应的方法
        # 3. 结合用户历史行为优化提示
        
        return result