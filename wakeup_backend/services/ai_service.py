import os
import json
from typing import Dict, List

import requests

# 简单的 AI 服务封装：优先调用 OpenAI（如有 API Key），否则返回模拟数据

OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
OPENAI_BASE_URL = os.environ.get("OPENAI_BASE_URL", "https://api.openai.com/v1")
OPENAI_MODEL = os.environ.get("OPENAI_MODEL", "gpt-4o-mini")


def _call_openai_chat(messages: List[Dict[str, str]]) -> str:
    if not OPENAI_API_KEY:
        raise RuntimeError("No OPENAI_API_KEY provided")
    url = f"{OPENAI_BASE_URL}/chat/completions"
    headers = {
        "Authorization": f"Bearer {OPENAI_API_KEY}",
        "Content-Type": "application/json",
    }
    payload = {
        "model": OPENAI_MODEL,
        "messages": messages,
        "temperature": 0.7,
    }
    resp = requests.post(url, headers=headers, data=json.dumps(payload), timeout=30)
    resp.raise_for_status()
    data = resp.json()
    return data["choices"][0]["message"]["content"].strip()


def generate_branches(topic: str) -> Dict:
    """
    返回格式：{
      "title": topic,
      "content": "不超过80字的定义",
      "children": ["子知识点1", "子知识点2", "子知识点3"]
    }
    """
    system = {
        "role": "system",
        "content": "你是一个专业的计算机科学导师。请严格按用户要求返回 JSON。",
    }
    user_prompt = (
        "请围绕核心主题 '" + topic + "'，为初学者生成3个最相关、最重要的学习问题（疑问句）。"
        "同时，为 '" + topic + "' 本身生成一段不超过80字的简洁定义。"
        "请严格按照以下JSON格式返回，不要包含任何额外的解释：\n"
        "{ \"title\": \"" + topic + "\", \"content\": \"定义\", \"children\": [\"问题1\", \"问题2\", \"问题3\"] }"
    )
    try:
        content = _call_openai_chat([system, {"role": "user", "content": user_prompt}])
        # 试图解析 JSON
        data = json.loads(content)
        children = data.get("children", [])
        children = [str(c) for c in children][:3]
        return {
            "title": data.get("title") or topic,
            "content": data.get("content") or "",
            "children": children,
        }
    except Exception:
        # 兜底模拟
        return {
            "title": topic,
            "content": f"{topic} 的核心概念与应用简介。",
            "children": [f"{topic} 基础", f"{topic} 进阶", f"{topic} 实践"],
        }


def ask_question(topic: str, question: str) -> Dict:
    """返回 {"answer": "..."} """
    system = {
        "role": "system",
        "content": "你是一个专业的计算机科学导师，请直接回答用户问题。",
    }
    user_prompt = f"上下文主题：{topic}\n用户问题：{question}\n请简明回答。"
    try:
        content = _call_openai_chat([system, {"role": "user", "content": user_prompt}])
        return {"answer": content}
    except Exception:
        return {"answer": f"关于 {topic}：{question} 的参考回答（示例）。"}

