from flask import Blueprint, jsonify, request
from models import db
from models.user import User
from models.course import Course
from utils.auth import validate_token

content_bp = Blueprint('content', __name__, url_prefix='/api')

@content_bp.route('/recommendations', methods=['GET'])
def get_recommendations():
    """获取推荐内容"""
    try:
        # 返回模拟推荐数据
        recommendations = [
            {
                'id': 1,
                'title': 'Python基础编程',
                'description': '适合初学者的Python编程课程',
                'image_url': None,
                'type': 'course',
                'target_id': 1,
                'priority': 1
            },
            {
                'id': 2,
                'title': 'JavaScript进阶',
                'description': 'Web开发必备的JavaScript高级技能',
                'image_url': None,
                'type': 'course', 
                'target_id': 2,
                'priority': 2
            },
            {
                'id': 3,
                'title': '数据结构与算法',
                'description': '程序员必备的计算机基础知识',
                'image_url': None,
                'type': 'course',
                'target_id': 3,
                'priority': 3
            }
        ]
        
        return jsonify(recommendations), 200
        
    except Exception as e:
        print(f"获取推荐内容错误: {e}")
        return jsonify({'message': f'获取推荐内容失败: {str(e)}'}), 500

@content_bp.route('/activities', methods=['GET'])
def get_activities():
    """获取活动内容"""
    try:
        # 返回模拟活动数据
        activities = [
            {
                'id': 1,
                'title': '编程挑战月',
                'description': '参与编程挑战，赢取丰厚奖励',
                'image_url': None,
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'status': 'active',
                'type': 'challenge'
            },
            {
                'id': 2,
                'title': '新课程发布',
                'description': 'AI与机器学习课程正式上线',
                'image_url': None,
                'start_date': '2024-01-15',
                'end_date': '2024-02-15',
                'status': 'active',
                'type': 'announcement'
            },
            {
                'id': 3,
                'title': '学习成果展示',
                'description': '分享你的学习项目，获得社区认可',
                'image_url': None,
                'start_date': '2024-01-10',
                'end_date': '2024-01-25',
                'status': 'active',
                'type': 'showcase'
            }
        ]
        
        return jsonify(activities), 200
        
    except Exception as e:
        print(f"获取活动内容错误: {e}")
        return jsonify({'message': f'获取活动内容失败: {str(e)}'}), 500

@content_bp.route('/home/<USER>', methods=['GET'])
def get_home_header():
    """获取首页头部内容"""
    try:
        header_data = {
            'title': '欢迎来到学习平台',
            'subtitle': '开启你的学习之旅',
            'banner_image': None,
            'user_greeting': '今天也要加油学习哦！'
        }
        
        return jsonify(header_data), 200
        
    except Exception as e:
        print(f"获取首页头部内容错误: {e}")
        return jsonify({'message': f'获取首页头部内容失败: {str(e)}'}), 500

@content_bp.route('/home/<USER>', methods=['GET'])
def get_home_content():
    """获取首页主要内容"""
    try:
        content_data = {
            'featured_courses': [
                {
                    'id': 1,
                    'name': 'Python入门',
                    'description': '零基础学Python编程',
                    'image_url': None,
                    'difficulty': 'beginner'
                },
                {
                    'id': 2,
                    'name': 'Web开发基础',
                    'description': '前端开发完整教程',
                    'image_url': None,
                    'difficulty': 'intermediate'
                }
            ],
            'latest_updates': [
                {
                    'title': '新增Python进阶课程',
                    'date': '2024-01-15',
                    'type': 'course_update'
                },
                {
                    'title': '系统维护公告',
                    'date': '2024-01-10',
                    'type': 'announcement'
                }
            ]
        }
        
        return jsonify(content_data), 200
        
    except Exception as e:
        print(f"获取首页内容错误: {e}")
        return jsonify({'message': f'获取首页内容失败: {str(e)}'}), 500