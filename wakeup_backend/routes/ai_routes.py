from flask import Blueprint, request, jsonify
from services.ai_service import generate_branches, ask_question

ai_bp = Blueprint('ai', __name__, url_prefix='/api/ai')

@ai_bp.route('/branches', methods=['POST'])
def branches():
    data = request.get_json(silent=True) or {}
    topic = data.get('topic', '').strip()
    if not topic:
        return jsonify({'error': 'topic required'}), 400
    res = generate_branches(topic)
    return jsonify(res)

@ai_bp.route('/ask', methods=['POST'])
def ask():
    data = request.get_json(silent=True) or {}
    topic = data.get('topic', '').strip()
    question = data.get('question', '').strip()
    if not topic or not question:
        return jsonify({'error': 'topic and question required'}), 400
    res = ask_question(topic, question)
    return jsonify(res)

