import json
import re
import random
from flask import Blueprint, request, jsonify
from models import db
from models.user import User
from utils.auth import create_token_with_expiry, validate_token
from datetime import datetime

auth_bp = Blueprint('auth_bp', __name__, url_prefix='/api/auth')

def validate_password(password):
    """验证密码强度"""
    if len(password) < 8 or len(password) > 18:
        return False, "密码长度需要在8-18个字符之间"
    
    if ' ' in password:
        return False, "密码不能包含空格"
    
    # 检查密码组成：必须包含大写字母、小写字母、数字和特殊字符
    has_upper = bool(re.search(r'[A-Z]', password))
    has_lower = bool(re.search(r'[a-z]', password))
    has_digit = bool(re.search(r'[0-9]', password))
    has_special = bool(re.search(r'[!@#$%^&*()_+=\-\[\]{}|;:,.<>?/]', password))
    
    if not (has_upper and has_lower and has_digit and has_special):
        return False, "密码必须包含大写字母、小写字母、数字和特殊字符"
    
    # 检查常见简单密码
    common_passwords = [
        'password', 'password123', '12345678', 'qwerty123', 'admin123',
        'letmein', 'welcome', 'monkey123', '123456789', 'iloveyou',
        'sunshine', '1q2w3e4r', 'princess', 'dragon', 'football',
        'baseball', 'superman', 'trustno1'
    ]
    
    if password.lower() in common_passwords:
        return False, "请勿使用常见简单密码"
    
    # 检查重复字符
    if re.search(r'(.)\1{3,}', password):
        return False, "密码不能包含重复的字符模式"
    
    return True, "密码验证通过"

def validate_username(username):
    """验证用户名"""
    if len(username) < 3 or len(username) > 15:
        return False, "用户名长度应为3-15个字符"
    
    if not re.match(r'^[a-zA-Z0-9._-]+$', username):
        return False, "用户名只能包含英文字母、数字和特殊符号(.、_、-)"
    
    if not re.match(r'^[a-zA-Z]', username):
        return False, "用户名必须以英文字母开头"
    
    if re.search(r'[._-]$', username):
        return False, "用户名不能以特殊符号结尾"
    
    if re.search(r'[._-]{2,}', username):
        return False, "用户名不能连续使用特殊符号"
    
    # 敏感词检查
    forbidden_names = [
        'admin', 'support', 'official', 'moderator', 'administrator',
        'system', 'root', 'manager', 'service', 'staff', 'team',
        'helpdesk', 'security', 'ceo', 'boss', 'wakeup', 'wake', 'up'
    ]
    
    if username.lower() in forbidden_names:
        return False, "用户名不能使用系统保留字或敏感词"
    
    for word in forbidden_names:
        if len(word) > 3 and word in username.lower():
            return False, "用户名不能包含系统保留字或敏感词"
    
    return True, "用户名验证通过"

def validate_phone(phone):
    """验证手机号格式"""
    # 如果包含国家代码，去除它进行验证
    if phone.startswith('+86'):
        # 中国大陆号码：+86 后面跟手机号
        actual_phone = phone[3:]  # 去掉 +86
    elif phone.startswith('+'):
        # 其他国家代码，暂时不支持
        return False, "暂不支持此国家/地区的手机号"
    else:
        # 没有国家代码，直接验证
        actual_phone = phone
    
    # 验证中国大陆手机号格式：1开头，第二位是3-9，总共11位数字
    if not re.match(r'^1[3-9]\d{9}$', actual_phone):
        return False, "请输入正确的手机号格式"
    
    return True, "手机号验证通过"

def validate_email(email):
    """验证邮箱格式"""
    email_pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    if not re.match(email_pattern, email):
        return False, "请输入正确的邮箱格式"
    return True, "邮箱验证通过"

@auth_bp.route('/register', methods=['POST'])
def register():
    """传统注册接口 - 已废弃"""
    return jsonify({
        'message': '传统注册已停用，请使用社交登录（Apple、Google、微信、QQ）',
        'code': 'LEGACY_REGISTRATION_DISABLED',
        'supported_methods': ['apple', 'google', 'wechat', 'qq'],
        'oauth_endpoint': '/api/auth/oauth_login'
    }), 410  # HTTP 410 Gone - 资源已不再可用

@auth_bp.route('/login', methods=['POST'])
def login_with_password():
    """传统密码登录接口 - 已废弃"""
    return jsonify({
        'message': '传统密码登录已停用，请使用社交登录（Apple、Google、微信、QQ）',
        'code': 'LEGACY_LOGIN_DISABLED',
        'supported_methods': ['apple', 'google', 'wechat', 'qq'],
        'oauth_endpoint': '/api/auth/oauth_login'
    }), 410  # HTTP 410 Gone - 资源已不再可用

@auth_bp.route('/profile', methods=['GET'])
def get_user_profile():
    """获取用户资料"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'message': '无效的授权头'}), 401
        
        token = auth_header.split(' ')[1]
        is_valid, message, user = validate_token(token)
        
        if not is_valid:
            return jsonify({'message': message}), 401
        
        return jsonify(user.to_dict()), 200
        
    except Exception as e:
        print(f"获取用户资料错误: {e}")
        return jsonify({'message': f'获取用户资料失败: {str(e)}'}), 500

@auth_bp.route('/check-account', methods=['POST'])
def check_account():
    """检查账号是否已注册"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'message': '请求数据不能为空'}), 400
        
        account = data.get('account', '').strip()
        account_type = data.get('type', '').strip()  # phone, email, username
        
        if not account:
            return jsonify({'message': '账号不能为空'}), 400
        
        user = None
        
        if account_type == 'phone':
            user = User.query.filter_by(phone=account).first()
        elif account_type == 'email':
            user = User.query.filter_by(email=account).first()
        elif account_type == 'username':
            user = User.query.filter_by(username=account).first()
        else:
            # 自动检测账号类型
            if '@' in account:
                user = User.query.filter_by(email=account).first()
            elif account.startswith('+') or account.isdigit():
                user = User.query.filter_by(phone=account).first()
            else:
                user = User.query.filter_by(username=account).first()
        
        return jsonify({
            'exists': user is not None,
            'account_type': 'phone' if user and user.phone == account else 
                          'email' if user and user.email == account else 
                          'username' if user and user.username == account else None
        }), 200
        
    except Exception as e:
        print(f"检查账号错误: {e}")
        return jsonify({'message': f'检查账号失败: {str(e)}'}), 500

@auth_bp.route('/send_code', methods=['POST'])
def send_code():
    """发送验证码"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'message': '请求数据不能为空'}), 400
        
        phone = data.get('phone', '').strip()
        
        if not phone:
            return jsonify({'message': '手机号不能为空'}), 400
        
        # 验证手机号格式
        valid, msg = validate_phone(phone)
        if not valid:
            return jsonify({'message': msg}), 400
        
        # 生成6位随机验证码
        code = str(random.randint(100000, 999999))
        
        # 查找或创建用户
        user = User.query.filter_by(phone=phone).first()
        if user:
            user.code = code
            user.updated_at = datetime.utcnow()
        else:
            user = User(phone=phone, code=code)
            db.session.add(user)
        
        db.session.commit()
        
        # 实际项目中这里应该调用短信发送接口
        # 为了演示，我们直接返回验证码
        print(f"验证码已生成: {code} (手机号: {phone})")
        
        return jsonify({
            'message': '验证码已发送',
            'code': code  # 生产环境中不应该返回验证码
        }), 200
        
    except Exception as e:
        db.session.rollback()
        print(f"发送验证码错误: {e}")
        return jsonify({'message': f'发送验证码失败: {str(e)}'}), 500

@auth_bp.route('/oauth_login', methods=['POST'])
def oauth_login():
    """OAuth社交登录"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'message': '请求数据不能为空'}), 400
        
        # 获取OAuth数据
        provider_id = data.get('provider_id', '').strip()
        open_id = data.get('open_id', '').strip()
        union_id = data.get('union_id', '').strip() if data.get('union_id') else None
        email = data.get('email', '').strip() if data.get('email') else None
        display_name = data.get('display_name', '').strip() if data.get('display_name') else None
        avatar_url = data.get('avatar_url', '').strip() if data.get('avatar_url') else None
        access_token = data.get('access_token', '').strip() if data.get('access_token') else None
        refresh_token = data.get('refresh_token', '').strip() if data.get('refresh_token') else None
        
        # 验证必需字段
        if not provider_id:
            return jsonify({'message': '登录提供商不能为空'}), 400
        if not open_id:
            return jsonify({'message': '第三方用户ID不能为空'}), 400
        
        # 验证provider_id合法性
        valid_providers = ['apple', 'wechat', 'qq', 'google']
        if provider_id not in valid_providers:
            return jsonify({'message': f'不支持的登录提供商: {provider_id}'}), 400
        
        # 查找现有用户
        existing_user = None
        
        # 1. 优先通过OAuth provider + open_id查找
        existing_user = User.query.filter_by(
            oauth_provider=provider_id,
            oauth_open_id=open_id
        ).first()
        
        # 2. 如果没找到且有union_id，通过union_id查找（微信）
        if not existing_user and union_id:
            existing_user = User.query.filter_by(
                oauth_provider=provider_id,
                oauth_union_id=union_id
            ).first()
        
        # 3. 如果没找到且有email，通过email查找现有账号进行绑定
        if not existing_user and email:
            existing_user = User.query.filter_by(email=email).first()
            if existing_user:
                # 绑定OAuth信息到现有账号
                existing_user.oauth_provider = provider_id
                existing_user.oauth_open_id = open_id
                existing_user.oauth_union_id = union_id
                existing_user.oauth_access_token = access_token
                existing_user.oauth_refresh_token = refresh_token
                existing_user.oauth_avatar_url = avatar_url
                existing_user.updated_at = datetime.utcnow()
        
        if existing_user:
            # 更新用户OAuth信息
            existing_user.oauth_access_token = access_token
            existing_user.oauth_refresh_token = refresh_token
            existing_user.oauth_avatar_url = avatar_url
            existing_user.updated_at = datetime.utcnow()
            
            # 生成新的登录令牌
            token = create_token_with_expiry(existing_user.id)
            existing_user.token = token
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'OAuth登录成功',
                'user_id': existing_user.id,
                'token': token,
                'is_new_user': False,
                'user': {
                    'id': existing_user.id,
                    'nickname': existing_user.nickname,
                    'email': existing_user.email,
                    'avatar_url': existing_user.oauth_avatar_url,
                    'provider': existing_user.oauth_provider
                }
            }), 200
        else:
            # 创建新用户（首次登录即注册）
            # 生成友好的昵称
            if display_name:
                generated_nickname = display_name
            else:
                provider_names = {
                    'apple': 'Apple用户',
                    'google': 'Google用户', 
                    'wechat': '微信用户',
                    'qq': 'QQ用户'
                }
                generated_nickname = f"{provider_names.get(provider_id, 'Social用户')}_{open_id[-6:]}"
            
            new_user = User(
                oauth_provider=provider_id,
                oauth_open_id=open_id,
                oauth_union_id=union_id,
                email=email,
                nickname=generated_nickname,
                oauth_access_token=access_token,
                oauth_refresh_token=refresh_token,
                oauth_avatar_url=avatar_url
            )
            
            # 生成登录令牌
            db.session.add(new_user)
            db.session.flush()  # 获取user id但不提交
            
            token = create_token_with_expiry(new_user.id)
            new_user.token = token
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': '欢迎！首次登录已自动完成注册',
                'user_id': new_user.id,
                'token': token,
                'is_new_user': True,
                'user': {
                    'id': new_user.id,
                    'nickname': new_user.nickname,
                    'email': new_user.email,
                    'avatar_url': new_user.oauth_avatar_url,
                    'provider': new_user.oauth_provider
                }
            }), 201
            
    except Exception as e:
        db.session.rollback()
        print(f"OAuth登录错误: {e}")
        return jsonify({'message': f'OAuth登录失败: {str(e)}'}), 500