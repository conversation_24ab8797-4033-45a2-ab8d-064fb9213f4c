import json
from flask import Blueprint, request, jsonify
from models import db
from models.user import User
import random, string, secrets
from utils.auth import create_token_with_expiry, token_required, validate_token

user_bp = Blueprint('user_bp', __name__, url_prefix='/api')

def filter_fields(data, allowed_fields=None):
    """过滤数据字段，只返回允许的字段"""
    if not allowed_fields:
        return data
    
    if isinstance(data, list):
        return [filter_fields(item, allowed_fields) for item in data]
    
    if isinstance(data, dict):
        return {k: v for k, v in data.items() if k in allowed_fields}
        
    return data

@user_bp.route('/user/<int:user_id>', methods=['GET'])
def get_user_info(user_id):
    """获取指定用户的详细信息"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'message': '无效的授权头'}), 401
        
        token = auth_header.split(' ')[1]
        is_valid, message, requesting_user = validate_token(token)
        
        if not is_valid:
            return jsonify({'message': message}), 401
        
        # 验证请求的用户ID是否与token中的用户匹配（确保用户只能查看自己的信息）
        if requesting_user.id != user_id:
            return jsonify({'message': '无权限访问该用户信息'}), 403
        
        user = User.query.get(user_id)
        if not user:
            return jsonify({'message': '用户不存在'}), 404
        
        # 返回用户的完整信息，包括昵称等
        user_info = {
            'id': user.id,
            'username': user.username,
            'nickname': user.nickname or user.username or f'用户{user.id}',  # 优先显示昵称
            'name': user.nickname or user.username or f'用户{user.id}',  # 兼容前端的name字段
            'phone': user.phone,
            'email': user.email,
            'gender': user.gender,
            'region': user.region,
            'country_code': user.country_code,
            'avatarUrl': None,  # 如果有头像URL字段，在这里添加
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'updated_at': user.updated_at.isoformat() if user.updated_at else None,
            # 添加一些统计信息
            'course_count': 0,  # TODO: 从相关表中获取实际数据
            'completed_count': 0,  # TODO: 从相关表中获取实际数据
            'study_hours': 0,  # TODO: 从相关表中获取实际数据
        }
        
        return jsonify(user_info), 200
        
    except Exception as e:
        print(f"获取用户信息错误: {e}")
        return jsonify({'message': f'获取用户信息失败: {str(e)}'}), 500

@user_bp.route('/send_code', methods=['POST'])
def send_code():
    data = request.get_json()
    phone = data.get('phone')
    
    if not phone:
        return jsonify({'message': '手机号不能为空'}), 400
    
    code = str(random.randint(100000, 999999))
    
    user = User.query.filter_by(phone=phone).first()
    if user:
        user.code = code
    else:
        user = User(phone=phone, code=code)
        db.session.add(user)
    
    db.session.commit()
    
    # 实际项目中这里应该调用短信发送接口
    # 为了演示，我们直接返回验证码
    return jsonify({
        'message': '验证码已发送',
        'code': code
    })

@user_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    phone = data.get('phone')
    code = data.get('code')
    
    if not phone or not code:
        return jsonify({'message': '手机号和验证码不能为空'}), 400
    
    user = User.query.filter_by(phone=phone).first()
    if not user or user.code != code:
        return jsonify({'message': '验证码错误'}), 401
    
    # 使用新的token生成函数
    token = create_token_with_expiry(user.id)
    user.token = token
    user.code = None  # 使验证码失效
    db.session.commit()
    
    # 只返回必要字段
    response_data = {
        'user_id': user.id,
        'token': token
    }
    
    return jsonify(response_data)

@user_bp.route('/logout', methods=['POST'])
def logout():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'message': '无效的授权头'}), 401
    
    token = auth_header.split(' ')[1]
    user = User.query.filter_by(token=token).first()
    
    if not user:
        return jsonify({'message': '未找到用户'}), 404
    
    user.token = None
    db.session.commit()
    
    return jsonify({'message': '注销成功'})

@user_bp.route('/profile', methods=['GET'])
def get_profile():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'message': '无效的授权头'}), 401
    
    token = auth_header.split(' ')[1]
    user = User.query.filter_by(token=token).first()
    
    if not user:
        return jsonify({'message': '未找到用户'}), 404
    
    # 仅返回需要的字段
    allowed_fields = ['id', 'phone', 'created_at']
    user_data = {
        'id': user.id,
        'phone': user.phone,
        'created_at': user.created_at.isoformat() if user.created_at else None
    }
    
    filtered_data = filter_fields(user_data, allowed_fields)
    return jsonify(filtered_data)

@user_bp.route('/profile', methods=['POST'])
def update_profile():
    """更新用户资料"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'message': '无效的授权头'}), 401
        
        token = auth_header.split(' ')[1]
        is_valid, message, user = validate_token(token)
        
        if not is_valid:
            return jsonify({'message': message}), 401
        
        data = request.get_json()
        if not data:
            return jsonify({'message': '请求数据不能为空'}), 400
        
        # 更新允许修改的字段
        if 'nickname' in data:
            user.nickname = data['nickname'].strip()
        if 'username' in data:
            # 检查用户名是否已被其他用户使用
            existing_user = User.query.filter_by(username=data['username']).first()
            if existing_user and existing_user.id != user.id:
                return jsonify({'message': '用户名已被使用'}), 400
            user.username = data['username'].strip()
        if 'gender' in data:
            user.gender = data['gender'].strip()
        if 'region' in data:
            user.region = data['region'].strip()
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': '个人资料更新成功'}), 200
        
    except Exception as e:
        db.session.rollback()
        print(f"更新用户资料错误: {e}")
        return jsonify({'message': f'更新用户资料失败: {str(e)}'}), 500

@user_bp.route('/verify_token', methods=['GET'])
def verify_token():
    """验证用户令牌是否有效"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'valid': False, 'message': '未提供有效的授权信息'}), 401
    
    token = auth_header.split(' ')[1]
    
    # 使用身份验证工具验证令牌
    is_valid, message, user = validate_token(token)
    
    if is_valid:
        return jsonify({'valid': True, 'user_id': user.id}), 200
    else:
        return jsonify({'valid': False, 'message': message}), 401
