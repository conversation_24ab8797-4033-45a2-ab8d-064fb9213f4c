"""Add category_level4_id to QuestionSet

Revision ID: 01fa64bbd527
Revises: 022f26cb85f8
Create Date: 2025-04-13 13:25:37.578499

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '01fa64bbd527'
down_revision = '022f26cb85f8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('category')
    with op.batch_alter_table('category_level1', schema=None) as batch_op:
        batch_op.drop_column('highlight')
        batch_op.drop_column('description')

    with op.batch_alter_table('category_level3', schema=None) as batch_op:
        batch_op.drop_column('highlight')
        batch_op.drop_column('description')

    with op.batch_alter_table('category_level4', schema=None) as batch_op:
        batch_op.drop_column('highlight')
        batch_op.drop_column('description')

    with op.batch_alter_table('category_level5', schema=None) as batch_op:
        batch_op.drop_column('highlight')
        batch_op.drop_column('description')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('category_level5', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('highlight', sa.TEXT(), nullable=True))

    with op.batch_alter_table('category_level4', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('highlight', sa.TEXT(), nullable=True))

    with op.batch_alter_table('category_level3', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('highlight', sa.TEXT(), nullable=True))

    with op.batch_alter_table('category_level1', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('highlight', sa.TEXT(), nullable=True))

    op.create_table('category',
    sa.Column('id', sa.INTEGER(), nullable=True),
    sa.Column('name', sa.TEXT(), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('highlight', sa.TEXT(), nullable=True),
    sa.Column('parent_id', sa.INTEGER(), nullable=True),
    sa.Column('level', sa.INTEGER(), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['category.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
