"""create user_course table

Revision ID: 022f26cb85f8
Revises: 
Create Date: 2025-04-12 20:16:27.222367

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '022f26cb85f8'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('category_level1',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=False),
    sa.Column('code', sa.String(length=6), nullable=True),
    sa.Column('token', sa.String(length=128), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('phone')
    )
    op.create_table('user_course',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('course_id', sa.Integer(), nullable=False),
    sa.Column('course_name', sa.String(length=100), nullable=True),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('cover_image', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('category_level2',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('level1_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('highlight', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['level1_id'], ['category_level1.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('category_level3',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('level2_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['level2_id'], ['category_level2.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('category_level4',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('level3_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['level3_id'], ['category_level3.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('category_level5',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('level4_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['level4_id'], ['category_level4.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('category_level5')
    op.drop_table('category_level4')
    op.drop_table('category_level3')
    op.drop_table('category_level2')
    op.drop_table('user_course')
    op.drop_table('user')
    op.drop_table('category_level1')
    # ### end Alembic commands ###
