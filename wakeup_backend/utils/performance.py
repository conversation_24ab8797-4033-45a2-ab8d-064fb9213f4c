import time
import functools
from flask import request, current_app, g
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('api-performance')

def performance_monitor():
    """装饰器，记录API性能指标"""
    def decorator(f):
        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            # 记录开始时间
            start_time = time.time()
            
            # 运行原始函数
            result = f(*args, **kwargs)
            
            # 计算执行时间
            execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 记录性能数据
            endpoint = request.endpoint
            method = request.method
            path = request.path
            
            # 记录到日志
            logger.info(f"API执行时间: {execution_time:.2f}ms | {method} {path} | Endpoint: {endpoint}")
            
            # 如果执行时间超过警告阈值（300ms），记录警告
            if execution_time > 300:
                logger.warning(f"API执行时间过长 ({execution_time:.2f}ms) | {method} {path}")
            
            # 添加执行时间到响应头
            if hasattr(result, 'headers'):
                result.headers['X-API-Response-Time'] = f"{execution_time:.2f}ms"
            
            return result
        return wrapper
    return decorator

def init_performance_monitoring(app):
    """初始化全局性能监控"""
    @app.before_request
    def before_request():
        g.start_time = time.time()

    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            execution_time = (time.time() - g.start_time) * 1000
            response.headers['X-API-Response-Time'] = f"{execution_time:.2f}ms"
            
            # 记录请求信息
            endpoint = request.endpoint
            method = request.method
            path = request.path
            
            # 对慢接口进行警告
            if execution_time > 300:
                logger.warning(f"慢接口警告 ({execution_time:.2f}ms) | {method} {path}")
            
            # 记录到日志
            logger.info(f"请求响应时间: {execution_time:.2f}ms | {method} {path}")
        
        return response 