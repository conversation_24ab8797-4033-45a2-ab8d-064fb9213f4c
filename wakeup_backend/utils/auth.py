import time
import secrets
from functools import wraps
from flask import request, jsonify
from models.user import User
from datetime import datetime, timedelta

# Token过期时间（秒）
TOKEN_EXPIRY = 7 * 24 * 60 * 60  # 7 天

def generate_token():
    """生成一个随机token"""
    return secrets.token_hex(32)

def create_token_with_expiry(user_id):
    """创建带有过期时间信息的token"""
    expiry_time = int(time.time()) + TOKEN_EXPIRY
    token = f"{generate_token()}.{expiry_time}.{user_id}"
    return token

def validate_token(token):
    """验证token是否有效，返回(is_valid, message, user)"""
    if not token:
        return False, "未提供令牌", None
    
    # 特殊处理测试token
    if token == "temp_skip_token_for_testing":
        test_user = User.query.filter_by(id=888).first()
        if test_user:
            print(f"测试用户找到: ID={test_user.id}, 昵称={test_user.nickname}")
            return True, "测试令牌有效", test_user
        else:
            print("测试用户未找到")
            all_users = User.query.all()
            print(f"数据库中所有用户: {[(u.id, u.nickname) for u in all_users]}")
            return False, "测试用户不存在", None
    
    # 从数据库查询用户
    user = User.query.filter_by(token=token).first()
    if not user:
        return False, "无效的授权令牌", None
    
    # 如果token包含过期时间
    if "." in token:
        try:
            _, expiry_str, _ = token.split(".")
            expiry_time = int(expiry_str)
            
            # 检查token是否过期
            if int(time.time()) > expiry_time:
                # 清除用户的过期token
                user.token = None
                from models import db
                db.session.commit()
                return False, "授权令牌已过期", None
            
        except Exception as e:
            print(f"令牌验证出错: {e}")
            return False, "令牌格式错误", None
    
    return True, "令牌有效", user

def token_required(f):
    """验证token的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'message': '未提供有效的授权信息'}), 401
        
        token = auth_header.split(' ')[1]
        is_valid, message, user = validate_token(token)
        
        if not is_valid:
            return jsonify({'message': message}), 401
        
        # 将用户对象传递给被装饰的函数
        return f(user=user, *args, **kwargs)
    
    return decorated_function 